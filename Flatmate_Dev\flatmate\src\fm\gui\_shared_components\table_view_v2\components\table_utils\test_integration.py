"""
Integration test for the refactored EnhancedFilterProxyModel with package-based parsing.
"""

from enhanced_filter_proxy_model import EnhancedFilterProxyModel


def test_integration():
    """Test the integration of package-based parsing with the filter proxy model."""
    print("Testing EnhancedFilterProxyModel integration...")
    
    # Create an instance
    proxy_model = EnhancedFilterProxyModel()
    
    # Test the search parser integration
    print(f"  Package parser available: {proxy_model._search_parser.is_available()}")
    print(f"  Using package parser: {proxy_model._use_package_parser}")
    
    # Test pattern matching
    test_cases = [
        ("coffee", "Starbucks coffee shop", True),
        ("coffee OR tea", "Tea house visit", True),
        ("coffee -decaf", "Decaf coffee", False),
        ('"coffee shop"', "Starbucks coffee shop", True),
        ("gas", "Gas station", True),
    ]
    
    print("\n  Testing pattern matching:")
    for pattern, data, expected in test_cases:
        try:
            result = proxy_model._check_pattern_match(data, pattern)
            status = "✓" if result == expected else "✗"
            print(f"    {status} '{pattern}' vs '{data}' → {result} (expected: {expected})")
        except Exception as e:
            print(f"    ✗ Error testing '{pattern}' vs '{data}': {e}")
    
    # Test legacy fallback
    print("\n  Testing legacy fallback:")
    proxy_model._use_package_parser = False
    
    legacy_test_cases = [
        ("coffee shop", "Starbucks coffee shop", True),
        ("coffee -decaf", "Regular coffee", True),
        ("coffee -decaf", "Decaf coffee", False),
    ]
    
    for pattern, data, expected in legacy_test_cases:
        try:
            result = proxy_model._legacy_check_pattern_match(data, pattern)
            status = "✓" if result == expected else "✗"
            print(f"    {status} '{pattern}' vs '{data}' → {result} (expected: {expected})")
        except Exception as e:
            print(f"    ✗ Error testing legacy '{pattern}' vs '{data}': {e}")
    
    # Test all-columns search
    print("\n  Testing all-columns search:")
    proxy_model._use_package_parser = True
    
    all_columns_test_cases = [
        ("coffee", "Starbucks coffee shop purchase", True),
        ("coffee OR tea", "Tea house visit", True),
        ("coffee -decaf", "Regular coffee purchase", True),
        ("coffee -decaf", "Decaf coffee purchase", False),
    ]
    
    for pattern, data, expected in all_columns_test_cases:
        try:
            result = proxy_model._legacy_all_columns_search(pattern, data)
            status = "✓" if result == expected else "✗"
            print(f"    {status} '{pattern}' vs '{data}' → {result} (expected: {expected})")
        except Exception as e:
            print(f"    ✗ Error testing all-columns '{pattern}' vs '{data}': {e}")


if __name__ == "__main__":
    print("Enhanced Filter Proxy Model Integration Test")
    print("=" * 50)
    
    test_integration()
    
    print("\nIntegration test completed!")
