"""
Test search column configuration flow.

This test validates the proper configuration flow for search columns:
1. <PERSON><PERSON>le configures visible columns and default search column
2. Table widget respects module configuration
3. Search dropdown shows only visible columns
4. Default search column is properly selected
"""

import sys
import os

# Add the project root to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../../../..'))

from filter_group import ColumnSelector


def test_column_selector_configuration():
    """Test the column selector configuration with proper column mapping."""
    print("Testing Column Selector Configuration")
    print("=" * 50)
    
    # Create column selector
    selector = ColumnSelector()
    
    # Test case 1: Categorize module configuration
    print("\n1. Testing Categorize Module Configuration")
    print("-" * 40)
    
    # Simulate what the categorize module provides
    visible_columns = ['Date', 'Details', 'Amount', 'Account', 'Tags']  # Display names
    column_mapping = {
        'date': 'Date',
        'details': 'Details', 
        'amount': 'Amount',
        'account': 'Account',
        'tags': 'Tags'
    }
    
    # Set columns (this is what the table widget calls)
    selector.set_columns(visible_columns, column_mapping)
    
    # Verify configuration
    print(f"Column count: {selector.count()}")
    print("Available columns:")
    for i in range(selector.count()):
        text = selector.itemText(i)
        data = selector.itemData(i)
        current = " (SELECTED)" if i == selector.currentIndex() else ""
        print(f"  {i}: '{text}' (data: '{data}'){current}")
    
    # Check if Details is selected by default
    current_text = selector.currentText()
    current_data = selector.currentData()
    
    success = current_text == 'Details'
    status = "✓" if success else "✗"
    print(f"\n{status} Default selection: '{current_text}' (expected: 'Details')")
    print(f"  Current data: '{current_data}'")
    
    # Test case 2: Column not found scenario
    print("\n2. Testing Column Not Found Scenario")
    print("-" * 40)
    
    selector2 = ColumnSelector()
    
    # Columns without Details
    no_details_columns = ['Date', 'Amount', 'Account']
    selector2.set_columns(no_details_columns)
    
    print(f"Column count: {selector2.count()}")
    current_text2 = selector2.currentText()
    
    fallback_success = current_text2 == 'Date'  # Should fallback to first column
    status2 = "✓" if fallback_success else "✗"
    print(f"{status2} Fallback selection: '{current_text2}' (expected: 'Date')")
    
    # Test case 3: Empty columns scenario
    print("\n3. Testing Empty Columns Scenario")
    print("-" * 40)
    
    selector3 = ColumnSelector()
    selector3.set_columns([])  # Empty list
    
    print(f"Column count: {selector3.count()}")
    if selector3.count() == 0:
        print("✓ Correctly handled empty column list")
    else:
        print("✗ Failed to handle empty column list")
    
    return success and fallback_success


def test_column_name_variants():
    """Test different column name variants and mappings."""
    print("\n" + "=" * 50)
    print("Testing Column Name Variants")
    print("=" * 50)
    
    selector = ColumnSelector()
    
    # Test different ways Details might appear
    test_cases = [
        # (columns, expected_selection, description)
        (['Details', 'Date', 'Amount'], 'Details', "Standard Details"),
        (['details', 'date', 'amount'], 'details', "Lowercase details"),
        (['Detail', 'Date', 'Amount'], 'Detail', "Singular Detail"),
        (['Date', 'Description', 'Amount'], 'Date', "No Details variant - fallback to first"),
    ]
    
    for columns, expected, description in test_cases:
        print(f"\n{description}:")
        print(f"  Columns: {columns}")
        
        selector.clear()
        selector.set_columns(columns)
        
        current = selector.currentText()
        success = current == expected
        status = "✓" if success else "✗"
        print(f"  {status} Selected: '{current}' (expected: '{expected}')")


def test_integration_flow():
    """Test the complete integration flow from module to search widget."""
    print("\n" + "=" * 50)
    print("Testing Complete Integration Flow")
    print("=" * 50)
    
    # Simulate the categorize module configuration
    print("\n1. Module Configuration (categorize)")
    module_config = {
        'default_visible_columns': ['date', 'details', 'amount', 'account', 'tags'],  # db_names
        'default_search_column': 'details'  # db_name
    }
    print(f"  Visible columns (db_names): {module_config['default_visible_columns']}")
    print(f"  Default search column: {module_config['default_search_column']}")
    
    # Simulate column mapping (db_name → display_name)
    print("\n2. Column Mapping (Columns class)")
    column_mapping = {
        'date': 'Date',
        'details': 'Details',
        'amount': 'Amount', 
        'account': 'Account',
        'tags': 'Tags'
    }
    print(f"  Column mapping: {column_mapping}")
    
    # Simulate DataFrame columns (display names)
    print("\n3. DataFrame Columns (after display name conversion)")
    dataframe_columns = ['Date', 'Details', 'Amount', 'Account', 'Tags']
    print(f"  DataFrame columns: {dataframe_columns}")
    
    # Simulate table widget processing
    print("\n4. Table Widget Processing")
    
    # Convert visible columns from db_names to display_names
    visible_display_columns = []
    for db_name in module_config['default_visible_columns']:
        if db_name in column_mapping:
            display_name = column_mapping[db_name]
            if display_name in dataframe_columns:
                visible_display_columns.append(display_name)
                print(f"  ✓ Mapped '{db_name}' → '{display_name}'")
            else:
                print(f"  ✗ Mapped '{db_name}' → '{display_name}' (not in DataFrame)")
        else:
            print(f"  ✗ No mapping for '{db_name}'")
    
    print(f"  Final search columns: {visible_display_columns}")
    
    # Test column selector with processed columns
    print("\n5. Column Selector Configuration")
    selector = ColumnSelector()
    selector.set_columns(visible_display_columns)
    
    current_selection = selector.currentText()
    expected_selection = column_mapping.get(module_config['default_search_column'], 'Details')
    
    success = current_selection == expected_selection
    status = "✓" if success else "✗"
    print(f"  {status} Search column selected: '{current_selection}' (expected: '{expected_selection}')")
    
    # Summary
    print(f"\n6. Integration Summary")
    print(f"  Module wants default: '{module_config['default_search_column']}'")
    print(f"  Mapped to display name: '{expected_selection}'")
    print(f"  Actually selected: '{current_selection}'")
    print(f"  Integration successful: {success}")
    
    return success


def main():
    """Run all configuration tests."""
    print("Search Column Configuration Test Suite")
    print("=" * 60)
    
    test1_success = test_column_selector_configuration()
    test_column_name_variants()
    test2_success = test_integration_flow()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    overall_success = test1_success and test2_success
    
    print(f"Column Selector Test: {'✓ PASS' if test1_success else '✗ FAIL'}")
    print(f"Integration Flow Test: {'✓ PASS' if test2_success else '✗ FAIL'}")
    print(f"Overall Result: {'✓ ALL TESTS PASSED' if overall_success else '✗ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 Configuration flow is working correctly!")
        print("The search column should now default to 'Details' properly.")
    else:
        print("\n⚠️  Configuration issues detected.")
        print("Review the column name mapping and selection logic.")


if __name__ == "__main__":
    main()
