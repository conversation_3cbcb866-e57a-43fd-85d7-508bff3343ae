# Categorize Module - UI Design Considerations

*Document Version: 1.0.0*  
*Created: 2025-07-14*  
*Status: Design Phase*
# ! this document needs review - we have moved to a cached system and pre built table view that is filtered live or on demand 
# todo : revie this design document and update it to reflect the current state of the module
## considerations:
optomise table view for large data sets - lazy load scrolling etc - note current files sizes are tiny and no scrolling issues currently noticeble...
consider a dedicated table view folder in app DOCS /architecture folder 

## Overview

This document outlines the architectural decisions and design considerations for enhancing the Categorize module's user interface, particularly focusing on filtering, layout, and user experience improvements.

## Core Architectural Principles

### Filter Application Strategy

The filtering system follows a two-tier architecture based on performance and user experience considerations:

#### **Tier 1: Database Query Filtering (Left Panel)**
- **Purpose**: Heavy filtering that significantly reduces dataset
- **Application**: Applied to database query → triggers table reload
- **Use Cases**: 
  - Date range selection
  - Account filtering
  - Any filter that reduces data by >50%
- **Benefits**: 
  - Better performance for large datasets
  - Reduces memory usage
  - Faster subsequent operations

#### **Tier 2: Table View Filtering (Top Toolbar)**
- **Purpose**: Light filtering and real-time search
- **Application**: Applied to already-loaded data → no database reload
- **Use Cases**:
  - Text search (description, notes)
  - Category/tag filtering
  - Amount range filtering
  - Status filtering
- **Benefits**:
  - Instant feedback
  - No database round-trips
  - Smooth user experience

### UI Layout Hierarchy

```
┌─────────────────────────────────────────────────────────────┐
│ Top Toolbar: Live Filters + Export                         │
│ [Search] [Category▼] [Tags▼] [More Options▼]    [Export] │
├─────────────────────────────────────────────────────────────┤
│ Left Panel │ Main Table View                              │
│ Database   │                                              │
│ Filters    │ Transaction Data                             │
│            │                                              │
│ [Date]     │ [Sortable Columns]                          │
│ [Account]  │                                              │
│ [Apply]    │                                              │
│ [Clear]    │                                              │
│ [▲Collapse]│                                              │
└─────────────────────────────────────────────────────────────┘
```

## Left Panel Design Specifications

### Real Estate Management

#### **Expanded State (Default)**
- **Width**: 200-300px (configurable)
- **Content**: Full filter controls with labels
- **Use Case**: Initial setup, filter configuration

#### **Collapsed State**
- **Width**: 40-60px (icon bar)
- **Content**: Filter status indicators + expand button
- **Use Case**: Maximized table view after filters applied
- **Trigger**: User clicks collapse button or auto-collapse after filter application

### Date Filter Component

#### **Quick Presets (Primary Interface)**
```
┌─────────────────────┐
│ Date Range          │
├─────────────────────┤
│ [Week] [Month] [3M] │
│ [6M]   [Year]  [All]│
├─────────────────────┤
│ ▼ Custom Range      │
│   From: [Date]      │
│   To:   [Date]      │
└─────────────────────┘
```

**Preset Options**:
- **Week**: Last 7 days
- **Month**: Last 30 days  
- **3M**: Last 3 months
- **6M**: Last 6 months
- **Year**: Last 12 months
- **All**: No date filtering

**Default Behavior**: 
- Start with "All" (no date filter) for maximum data visibility
- Remember last used preset as user preference
- Visual indication of active preset

#### **Custom Date Range**
- Collapsed by default
- Expands when "Custom Range" clicked
- Calendar popup enabled
- Validates date range (start ≤ end)

### Account Filter Component

```
┌─────────────────────┐
│ Account             │
├─────────────────────┤
│ ○ All Accounts      │
│ ○ Checking (1234)   │
│ ○ Savings (5678)    │
│ ○ Credit (9012)     │
└─────────────────────┘
```

**Features**:
- Radio button selection
- "All Accounts" as default
- Show account numbers/names from database
- Remember last selection
- Optional: Show transaction count per account

### Filter Actions

```
┌─────────────────────┐
│ [Apply Filters]     │
│ [Clear All]         │
│ [▲ Collapse Panel]  │
└─────────────────────┘
```

**Apply Filters**: 
- Triggers database query with selected filters
- Shows loading indicator
- Reloads table view with filtered data

**Clear All**:
- Resets all filters to defaults (All dates, All accounts)
- Triggers database reload with no filters
- Visual confirmation of action

**Collapse Panel**:
- Minimizes left panel to icon bar
- Shows filter status indicators when collapsed
- Quick expand on hover or click

## Top Toolbar Design Specifications

### Live Filtering Controls

```
[🔍 Search...] [Category ▼] [Tags ▼] [More Options ▼]    [Export]
```

#### **Search Box**
- Real-time search as user types
- Searches: description, notes, category, tags
- Debounced (300ms) to prevent excessive filtering
- Clear button (×) when text present

#### **Category Dropdown**
- Shows all available categories
- Multi-select capability
- "All Categories" option
- Filter count indicator

#### **Tags Dropdown**  
- Shows all available tags
- Multi-select capability
- "All Tags" option
- Recent tags at top

#### **More Options Dropdown**
- Amount range slider
- Transaction type filters
- Advanced search options
- "Show All" to clear table filters

#### **Export Button**
- Exports currently visible data (after all filters applied)
- Multiple format options
- Maintains current position (top-right)

## Component Architecture

### Shared Components to Create

#### **DateFilterPane**
```python
class DateFilterPane(QWidget):
    """Reusable date filtering component with presets and custom range."""
    
    # Signals
    filter_changed = Signal(dict)  # {'start_date': date, 'end_date': date}
    preset_selected = Signal(str)  # 'week', 'month', '3m', etc.
```

#### **AccountFilterPane**  
```python
class AccountFilterPane(QWidget):
    """Account selection component with radio buttons."""
    
    # Signals
    account_changed = Signal(str)  # account_id or None for "All"
```

#### **CollapsibleFilterPanel**
```python
class CollapsibleFilterPanel(QWidget):
    """Container for filter components with collapse/expand functionality."""
    
    # Signals
    collapsed = Signal(bool)
    filters_applied = Signal(dict)
```

### Integration with Existing Components

- **Leverage existing**: `SecondaryButton`, `ActionButton`, `LabeledCheckBox`
- **Extend**: `OptionMenuWithLabel` for account selection
- **Create new**: Date preset buttons, collapsible panel logic

## Implementation Phases

### **Phase A: Enhanced Date Filter Component**
1. Create `DateFilterPane` shared component
2. Implement preset buttons with proper styling
3. Add custom date range (collapsible)
4. Integrate with categorize left panel
5. Add filter persistence

### **Phase B: Account Filter Enhancement**
1. Create `AccountFilterPane` component
2. Load accounts from database
3. Implement selection persistence
4. Add visual enhancements

### **Phase C: Panel Behavior & Collapsibility**
1. Create `CollapsibleFilterPanel` wrapper
2. Implement collapse/expand animations
3. Add filter status indicators for collapsed state
4. Integrate with main layout

### **Phase D: Database Query Integration**
1. Modify presenter to apply left panel filters to database queries
2. Implement table reload on filter changes
3. Add loading states and error handling
4. Optimize query performance

### **Phase E: Top Toolbar Live Filters**
1. Enhance existing table view toolbar
2. Add real-time search functionality
3. Implement category/tag dropdowns
4. Add "More Options" expandable section

## Configuration Integration

### Config Keys for Filter Behavior

```python
# Default filter behavior
'categorize.filters.default_date_preset': 'all'  # 'week', 'month', '3m', etc.
'categorize.filters.remember_last_selection': True
'categorize.filters.auto_collapse_after_apply': False

# Panel layout
'categorize.ui.left_panel_width_expanded': 250
'categorize.ui.left_panel_width_collapsed': 50
'categorize.ui.left_panel_default_state': 'expanded'

# Performance settings
'categorize.filters.database_query_timeout': 30
'categorize.filters.live_search_debounce_ms': 300
```

## User Experience Considerations

### **Progressive Disclosure**
- Start simple (presets) → reveal complexity (custom dates) as needed
- Collapse panel after filters applied to maximize table space
- Clear visual hierarchy: Database filters (left) → Table filters (top)

### **Performance Feedback**
- Loading indicators for database queries
- Instant feedback for table-level filters
- Clear indication of active filters
- Transaction count updates

### **Accessibility**
- Keyboard navigation for all filter controls
- Screen reader support for filter states
- High contrast mode compatibility
- Tooltips for complex controls

## Future Considerations

### **Advanced Features**
- Saved filter presets ("My Filters")
- Filter history and quick restore
- Bulk filter operations
- Filter sharing between users

### **Mobile Responsiveness**
- Collapsible panels for small screens
- Touch-friendly filter controls
- Simplified preset selection

### **Integration Points**
- Export functionality respects all active filters
- Search integration with categorization
- Filter state in URL for bookmarking
- API endpoints for filter operations

---

*This document serves as the architectural foundation for categorize module UI enhancements and should be updated as implementation progresses.*
