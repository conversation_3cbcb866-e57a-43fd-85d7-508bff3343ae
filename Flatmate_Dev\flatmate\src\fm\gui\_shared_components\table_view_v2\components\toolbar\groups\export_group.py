"""
Export Group

Handles all export-related functionality for table data.
Contains export button with menu for different export formats.
"""

from PySide6.QtCore import Signal, QSize
from PySide6.QtGui import QAction, QIcon
from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QMenu


class ExportButton(QPushButton):
    """Button for showing export menu with CSV and Excel options."""

    csv_export_requested = Signal()  # Emitted when CSV export is requested
    excel_export_requested = Signal()  # Emitted when Excel export is requested

    def __init__(self, text="", parent=None):
        """Initialize the export button with export icon only."""
        super().__init__(text, parent)
        self.clicked.connect(self._show_export_menu)
        self.setToolTip("Export Data")

        # Load export icon
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer

            export_icon_path = icon_manager.get_toolbar_icon("export")
            export_icon = IconRenderer.load_icon(export_icon_path, QSize(16, 16))
            self.setIcon(export_icon)
            self.setIconSize(QSize(16, 16))

        except Exception as e:
            print(f"Warning: Could not load export icon: {e}")

        # Apply consistent styling for icon-only button
        self.setStyleSheet("""
            QPushButton {
                background-color: #2A2A2A;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 6px;
                min-width: 32px;
                max-width: 32px;
                min-height: 32px;
                max-height: 32px;
            }
            QPushButton:hover {
                background-color: #3A3A3A;
                border-color: #3B8A45;
            }
            QPushButton:pressed {
                background-color: #1A1A1A;
            }
        """)
    
    def _show_export_menu(self):
        """Show export options menu."""
        menu = QMenu(self)
        
        # CSV export action
        csv_action = QAction("Export to CSV", self)
        csv_action.triggered.connect(self.csv_export_requested.emit)
        menu.addAction(csv_action)
        
        # Excel export action
        excel_action = QAction("Export to Excel", self)
        excel_action.triggered.connect(self.excel_export_requested.emit)
        menu.addAction(excel_action)
        
        # Show menu relative to button
        menu.exec(self.mapToGlobal(self.rect().bottomRight()))


class ExportGroup(QWidget):
    """Group focused solely on export functionality."""
    
    # Signals for external communication
    csv_export_requested = Signal()
    excel_export_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the export group."""
        super().__init__(parent)

        # Set CSS class for styling
        self.setObjectName("ExportGroup")

        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)  # Reduced from 5px to 4px for tighter spacing
        
        # Export button
        self.export_button = ExportButton()
        layout.addWidget(self.export_button)
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Forward signals from export button
        self.export_button.csv_export_requested.connect(
            self.csv_export_requested)
        self.export_button.excel_export_requested.connect(
            self.excel_export_requested)
