# Implementation Tasks

This document outlines the specific tasks required to implement the table search and filtering functionality, organized by implementation phase.

## ✅ **IMPLEMENTED (Phase 1)**

### Core Functionality
- [x] **Filter Persistence** - Settings saved between sessions
- [x] **AND Logic** - Space-separated terms (e.g., "coffee shop")
- [x] **EXCLUDE Logic** - Dash-prefixed terms (e.g., "-refund")
- [x] **Case-insensitive Search** - Matches regardless of case
- [x] **Live Filtering** - Results update as you type
- [x] **Column Selection** - Filter specific columns or all columns

### User Experience
- [x] **Default Column** - Defaults to "Details" instead of "All Columns"
- [x] **Intuitive Placeholder** - Simple, example-based hint text
- [x] **Performance Optimization** - Fast "All Columns" search
- [x] **State Management** - Proper UI state restoration

### Technical Implementation
- [x] **TableConfig Integration** - Uses app-wide configuration pattern
- [x] **Signal/Slot Architecture** - Proper Qt event handling
- [x] **Pattern Parsing** - Efficient term parsing and matching
- [x] **Error Handling** - Graceful handling of edge cases

---

## 🚧 **PLANNED (Phase 2)**

### Advanced Operators
- [ ] **OR Logic** - Pipe-separated terms (e.g., "coffee|tea")
- [ ] **Bracketing/Grouping** - Parentheses for complex expressions (e.g., "(coffee|tea) -decaf")
- [ ] **Exact Match** - Quoted terms for literal matching (e.g., '"coffee shop"')
- [ ] **Wildcard Support** - Asterisk for pattern matching (e.g., "coff*")

### Enhanced UI
- [ ] **Search Term Constructor** - Visual query builder widget
- [ ] **Operator Hints** - Dynamic help text based on current input
- [ ] **Syntax Highlighting** - Color-coded terms in the input field
- [ ] **Auto-completion** - Suggest terms based on data content

### Advanced Features
- [ ] **Saved Filters** - Named filter presets for common searches
- [ ] **Filter History** - Quick access to recent filter patterns
- [ ] **Column-specific Operators** - Date ranges, numeric comparisons
- [ ] **Regex Support** - Regular expression matching for power users

---

## 📋 **CURRENT SYNTAX (Phase 1)**

### Supported Operators
| Operator | Syntax | Example | Description |
|----------|--------|---------|-------------|
| **AND** | `space` | `coffee shop` | Both terms must be present |
| **EXCLUDE** | `-term` | `-refund` | Exclude rows containing term |
| **Combined** | `term -exclude` | `coffee -decaf` | Coffee but not decaf |

### Examples
- `starbucks` → Find all Starbucks transactions
- `coffee shop` → Find transactions with both "coffee" AND "shop"
- `restaurant -mcdonalds` → Find restaurant transactions except McDonald's
- `-transfer -payment` → Exclude transfers and payments

---

## 🎯 **PLANNED SYNTAX (Phase 2)**

### Additional Operators
| Operator | Syntax | Example | Description |
|----------|--------|---------|-------------|
| **OR** | `term\|term` | `coffee\|tea` | Either term can be present |
| **GROUP** | `(terms)` | `(coffee\|tea) -decaf` | Group operations |
| **EXACT** | `"term"` | `"coffee shop"` | Exact phrase match |
| **WILDCARD** | `term*` | `coff*` | Pattern matching |

### Complex Examples
- `(coffee|tea) -decaf` → Coffee or tea, but not decaf
- `"gas station" OR "fuel stop"` → Exact phrases with OR
- `restaurant -(mcdonalds|kfc|subway)` → Restaurants except fast food
- `amount:>100 AND category:dining` → Column-specific filters

---

## 🔧 **TECHNICAL IMPLEMENTATION STATUS**

### Phase 1 Implementation
```python
# Current pattern parsing (implemented)
def _parse_filter_pattern(self, pattern: str) -> tuple[list[str], list[str]]:
    """Parse pattern into AND terms and EXCLUDE terms."""
    # Handles: "coffee shop -refund" → (['coffee', 'shop'], ['refund'])
```

### Phase 2 Implementation (Package-Based Approach)
```python
# Package-based search implementation (planned)
class SearchQueryParser:
    """Wrapper for external boolean search parser with preprocessing."""

    def __init__(self):
        self.parser = LuceneQueryParser()  # Or selected package

    def parse(self, query_string: str) -> FilterExpression:
        """Parse user query into structured filter expression."""
        # 1. Preprocess: normalize operator synonyms
        normalized = self._preprocess_operators(query_string)
        # 2. Parse using external package
        parsed = self.parser.parse(normalized)
        # 3. Convert to our FilterExpression format
        return self._convert_to_filter_expression(parsed)

    def _preprocess_operators(self, query: str) -> str:
        """Convert user-friendly operators to package syntax."""
        # space → AND, | → OR, - → NOT, etc.
        return normalized_query
```

---

## 🚀 **IMPLEMENTATION ROADMAP**

### Immediate (Next Sprint) - Package Integration
1. **Parser Package Selection** - Research and select boolean search library
2. **Preprocessing Layer** - Implement operator synonym normalization
3. **Package Integration Wrapper** - Create SearchQueryParser class
4. **Backward Compatibility** - Ensure Phase 1 syntax continues working

### Short Term (Next Month) - Enhanced Features
1. **OR Functionality** - Leverage package's OR operator support
2. **Basic Bracketing** - Use package's grouping capabilities
3. **Performance Optimization** - Profile and optimize package integration
4. **Updated Documentation** - User guide with new operators

### Long Term (Future Releases) - Advanced Features
1. **Search Term Constructor UI** - Visual query builder
2. **Enhanced Error Messages** - Better syntax error feedback from package
3. **Saved Filter Presets** - Named filter management
4. **Column-specific Operators** - Date/numeric comparisons
5. **Advanced UI Features** - Syntax highlighting, auto-complete

---

## 📊 **METRICS & PERFORMANCE**

### Current Performance (Phase 1)
- **Simple Search**: < 10ms response time
- **AND Logic**: < 15ms response time
- **All Columns**: < 50ms response time (optimized)
- **Memory Usage**: Minimal overhead

### Target Performance (Phase 2)
- **OR Logic**: < 20ms response time
- **Complex Expressions**: < 100ms response time
- **UI Constructor**: < 5ms interaction response
- **Memory Usage**: < 10% increase from Phase 1

---

## 🐛 **KNOWN LIMITATIONS (Phase 1)**

### Current Limitations
1. **No OR Logic** - Cannot search for "coffee OR tea"
2. **No Grouping** - Cannot use parentheses for complex expressions
3. **No Exact Match** - Cannot force literal string matching
4. **Limited Operators** - Only AND and EXCLUDE supported

### Workarounds
1. **Multiple Searches** - Run separate searches for OR conditions
2. **Broader Terms** - Use more general terms instead of complex expressions
3. **Manual Filtering** - Use additional table features for complex needs

---

## 📝 **NOTES FOR DEVELOPERS**

### Package-Based Architecture
1. **Parser Selection**: Evaluate packages based on features, performance, and maintenance
2. **Preprocessing Layer**: Handle operator synonym normalization before package parsing
3. **Wrapper Design**: Create clean interface that abstracts package-specific details
4. **Error Handling**: Translate package errors to user-friendly messages

### Adding New Operators
1. Update `_preprocess_operators()` method for synonym normalization
2. Ensure selected package supports the desired operator
3. Add unit tests for new syntax patterns
4. Update user documentation and examples

### UI Enhancements
1. Consider user experience impact of package-based parsing
2. Maintain backward compatibility with Phase 1 syntax
3. Test with real user data and complex expressions
4. Provide clear migration path and error feedback

### Performance Considerations
1. Profile package parsing performance vs. custom implementation
2. Consider caching parsed expressions for repeated patterns
3. Optimize preprocessing step for common use cases
4. Monitor memory usage with large datasets and complex queries
5. Evaluate package overhead and initialization costs

---

**Last Updated**: 2025-07-18  
**Next Review**: When Phase 2 development begins
