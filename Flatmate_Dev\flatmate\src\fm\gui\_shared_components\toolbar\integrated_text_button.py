"""
Integrated Text Button Component

Specialized button class for embedding inside text input fields.
Smaller and more focused than BaseToolbarButton, designed specifically
for integration within QLineEdit widgets.
"""

from PySide6.QtCore import QSize
from PySide6.QtWidgets import <PERSON><PERSON>ush<PERSON>utton
from typing import Optional


class IntegratedTextButton(QPushButton):
    """Specialized button for embedding inside text input fields.
    
    Smaller than standard toolbar buttons and designed specifically
    for integration within QLineEdit widgets using addAction() or
    custom composite layouts.
    """
    
    # Style variants optimized for text field integration
    STYLE_VARIANTS = {
        "apply": {
            "background": "#3B8A45",
            "border": "none",
            "hover_bg": "#4BA357",
            "pressed_bg": "#2E6E37",
            "size": 24,  # Slightly bigger for better visibility
            "icon_size": 16  # Explicit icon size
        },
        "clear": {
            "background": "transparent",
            "border": "none",
            "hover_bg": "rgba(255, 255, 255, 0.1)",
            "pressed_bg": "rgba(255, 255, 255, 0.2)",
            "size": 22,  # Slightly bigger
            "icon_size": 14
        },
        "search": {
            "background": "transparent",
            "border": "none",
            "hover_bg": "transparent",  # Decorative only
            "pressed_bg": "transparent",
            "size": 22,  # Consistent with clear
            "icon_size": 14
        }
    }
    
    def __init__(self, icon_name: Optional[str] = None, tooltip: Optional[str] = None,
                 style_variant: str = "apply", parent=None):
        """Initialize integrated text button.
        
        Args:
            icon_name: Name of icon in toolbar category
            tooltip: Tooltip text for accessibility
            style_variant: Style variant ("apply", "clear", "search")
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Get style configuration
        style_config = self.STYLE_VARIANTS.get(style_variant, self.STYLE_VARIANTS["apply"])
        button_size = style_config["size"]
        icon_size = style_config["icon_size"]

        # Optimized sizing for text field integration
        self.setFixedSize(button_size, button_size)

        # Set tooltip if provided
        if tooltip:
            self.setToolTip(tooltip)

        # Load icon if provided
        if icon_name:
            self._load_toolbar_icon(icon_name, icon_size)

        # Apply styling
        self._apply_styling(style_variant)
    
    def _load_toolbar_icon(self, icon_name: str, icon_size: int) -> None:
        """Load icon using standardized icon management system.

        Args:
            icon_name: Name of the icon to load from toolbar category
            icon_size: Explicit icon size to use
        """
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer

            icon_path = icon_manager.get_toolbar_icon(icon_name)
            icon = IconRenderer.load_icon(icon_path, QSize(icon_size, icon_size))
            self.setIcon(icon)
            self.setIconSize(QSize(icon_size, icon_size))

        except Exception as e:
            print(f"Warning: Could not load integrated text button icon '{icon_name}': {e}")
    
    def _apply_styling(self, variant: str) -> None:
        """Apply styling optimized for text field integration.
        
        Args:
            variant: Style variant name
        """
        style = self.STYLE_VARIANTS.get(variant, self.STYLE_VARIANTS["apply"])
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {style["background"]};
                color: white;
                border: {style["border"]};
                border-radius: 3px;  /* Slightly larger radius for better appearance */
                padding: 3px;        /* Rational padding for better icon positioning */
                margin: 2px;         /* Rational margin for proper spacing */
            }}
            QPushButton:hover {{
                background-color: {style["hover_bg"]};
            }}
            QPushButton:pressed {{
                background-color: {style["pressed_bg"]};
            }}
            QPushButton:disabled {{
                background-color: transparent;
                color: #888888;
            }}
        """)


# Convenience factory functions for common integrated text buttons

def create_integrated_apply_button(parent=None) -> IntegratedTextButton:
    """Create an apply button optimized for text field integration.
    
    Returns:
        IntegratedTextButton configured as apply button
    """
    return IntegratedTextButton(
        icon_name="check",
        tooltip="Apply filter",
        style_variant="apply",
        parent=parent
    )


def create_integrated_clear_button(parent=None) -> IntegratedTextButton:
    """Create a clear button optimized for text field integration.
    
    Returns:
        IntegratedTextButton configured as clear button
    """
    return IntegratedTextButton(
        icon_name="clear",
        tooltip="Clear text",
        style_variant="clear",
        parent=parent
    )


def create_integrated_search_icon(parent=None) -> IntegratedTextButton:
    """Create a decorative search icon for text field integration.
    
    Returns:
        IntegratedTextButton configured as decorative search icon
    """
    button = IntegratedTextButton(
        icon_name="search",
        tooltip="Search",
        style_variant="search",
        parent=parent
    )
    button.setEnabled(False)  # Decorative only
    return button


class IntegratedTextFieldAction:
    """Helper class for creating QLineEdit actions with IntegratedTextButton styling.
    
    This class provides utilities for creating QAction objects that can be added
    to QLineEdit widgets using the addAction() method, with styling that matches
    IntegratedTextButton appearance.
    """
    
    @staticmethod
    def create_apply_action(line_edit, callback=None):
        """Create an apply action for QLineEdit.addAction().
        
        Args:
            line_edit: QLineEdit widget to add action to
            callback: Function to call when action is triggered
            
        Returns:
            QAction: Configured action ready for addAction()
        """
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            from PySide6.QtGui import QAction
            
            icon_path = icon_manager.get_toolbar_icon("check")
            icon = IconRenderer.load_icon(icon_path, QSize(16, 16))  # Slightly bigger
            
            action = line_edit.addAction(icon, line_edit.TrailingPosition)
            action.setToolTip("Apply filter")
            
            if callback:
                action.triggered.connect(callback)
            
            return action
            
        except Exception as e:
            print(f"Warning: Could not create integrated apply action: {e}")
            return None
    
    @staticmethod
    def create_search_action(line_edit):
        """Create a decorative search action for QLineEdit.addAction().
        
        Args:
            line_edit: QLineEdit widget to add action to
            
        Returns:
            QAction: Configured decorative search action
        """
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            from PySide6.QtGui import QAction
            
            icon_path = icon_manager.get_toolbar_icon("search")
            icon = IconRenderer.load_icon(icon_path, QSize(14, 14))  # Keep smaller for decorative
            
            action = line_edit.addAction(icon, line_edit.LeadingPosition)
            action.setToolTip("Search")
            # Don't connect any callback - decorative only
            
            return action
            
        except Exception as e:
            print(f"Warning: Could not create integrated search action: {e}")
            return None
