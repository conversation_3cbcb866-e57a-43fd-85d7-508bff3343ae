# app test notes
## categorise module
### table view tool bar 
    - [ ] the search tool should have a search icon on the left
    - [ ] the clear button should be an x icon and is not apparent, insidr the text box is fine, but not neccesary - it could be to the right of the text box
    the aply button 

    - [ ] the apply button looks good - does appear outside the box and thats fine, but I'm leaning toward the
    - [ ] apply button appearing inside the text box hard right (if possible - research integrited icons in pyside 6 qt ) 
    -[ ] the clear button would sit outside aligned to the right and always be present, 
    - the search label/button on the right still has text and no icon
    - to facilitate this it may be neccesary to put the text search related icons inside a container and have *that* exand to fit available space
    - [ ] the export button should be be the export notes icon not a download icon
    - [ ] the text related group should expand to fill the available space, prioritising the text entry field

--- 
issues raised:

# repeated difficulty in succesfully implementing these gui refinements suggests a potential architecture issue
- the architecture, folder structure, filenaming convntions 
and icon management system need to be dev friendly and maintainable 
a tool bar button class needs to be created so that tool bar buttons can be easily chnaged and modified and remin consistent 

#feature implementation protocol might need refinment

# a gui refinement protocol might be worth considering 
 - documents on the gui system in DOCS / architecture 
  may need to be included, but they may need checking and rationalising 
  