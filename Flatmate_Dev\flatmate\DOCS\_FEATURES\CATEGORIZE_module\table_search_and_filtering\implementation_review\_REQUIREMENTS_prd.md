# Table Search & Filtering Requirements (PRD)

## User Story
As a user, I want to quickly search and filter table data in the Categorise view, using both simple and advanced logic, so that I can efficiently find relevant transactions.

## Acceptance Criteria
- [x] Quick search supports AND (implicit) and exclude (`-term`) logic with live filtering.
- [x] Advanced logic (OR, parentheses, operators) disables live filtering and requires explicit apply.
- [x] Visual cue/button appears when live filtering is disabled.
- [x] Only user-meaningful columns are shown in the table.
- [x] Filtering and sorting are performant (no UI lag or freeze).
- [x] Errors are clearly communicated to the user.

## Success Metrics
- Live filtering response time < 200ms for simple queries ✅
- No UI freeze or lag for any filter ✅
- User feedback confirms intuitive search experience ✅

## Implementation Status: COMPLETE ✅

### Key Features Implemented:
1. **Smart Live Filtering**: Automatically detects simple vs complex queries
2. **Dynamic Apply Button**: Shows/hides based on query complexity
3. **Visual Feedback**: Input field styling changes for advanced mode
4. **System Column Filtering**: Hides internal columns from user view
5. **Performance Optimizations**: Debouncing and error handling prevent UI freezes
6. **Centralized Logic**: Uses search query parser as single source of truth
