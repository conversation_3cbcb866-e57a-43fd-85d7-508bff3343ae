# Discussion: SQLite vs PostgreSQL – Local Use and Mirroring Strategy

## Context

- The app currently uses SQLite for local, lightweight data storage.
- There is a potential future need to mirror or migrate the database to a hosted PostgreSQL instance for multi-user access and permission control.

## SQLite: Merits & Limitations

**Merits**
- Extremely lightweight: minimal install, bundled with Python, no server required.
- Fast and responsive for single-user, local workloads.
- Simple file-based storage: easy to back up and move.

**Limitations**
- Not designed for concurrent, multi-user, or networked access.
- No built-in user management or permission system.
- Limited scalability and advanced SQL features.

## PostgreSQL: Merits & Limitations

**Merits**
- Robust, production-grade RDBMS with advanced SQL features.
- Designed for client-server, multi-user, and networked scenarios.
- Fine-grained user/role management and permissions.
- Supports replication, backups, and easy migration to cloud/hosted platforms.

**Limitations**
- Larger install size and resource footprint.
- Requires running a background service, even for local use.
- Slightly higher latency for local operations.

## Overhead Comparison

| Feature         | SQLite         | PostgreSQL      |
|-----------------|---------------|-----------------|
| Install Size    | ~1MB           | 100–300MB+      |
| Service Needed  | No             | Yes (background)|
| RAM Usage       | Minimal        | 50–100MB+       |
| Startup Time    | Instant        | Few seconds     |
| Local Speed     | Very fast      | Fast, small overhead |

## Recommended Approach

- **Continue using SQLite** for local, single-user workflows to maximise speed and simplicity.
- **Abstract database access** via repository/service interfaces to allow seamless backend swapping.
- When multi-user/networked access is needed, **migrate or mirror to PostgreSQL**.
- Plan for a migration/sync script or tool to transfer data from SQLite to PostgreSQL when required.

## Migration/Mirroring Considerations

- Ensure schema compatibility between SQLite and PostgreSQL (watch for type differences).
- Use tools like `pgloader` or custom scripts for data migration.
- Test the migration process early to identify potential issues.

## Action Points

- Maintain clean separation between business logic and database backend.
- Document the migration/sync process for future use.
- Revisit the decision if/when multi-user or remote access becomes a priority.
