# Export Functionality Requirements

## Core Requirements

- [ ] Users can export the currently visible table from the Categorise view, including all applied filters and sorts.
- [ ] Export is triggered via the existing export button in the table view toolbar.
- [ ] Exported data matches exactly what is displayed (columns, order, filtered/sorted rows).
- [ ] Supported export format: CSV (Excel optional for phase 1).
- [ ] Users are prompted to choose the file destination and format via a file dialog.
- [ ] Errors during export are clearly communicated to the user (no silent failures).

## Non-Goals (Phase 1)

- No new UI panels or modules for export configuration.
- No support for export presets, templates, or batch export in phase 1.

## Extensibility

- The implementation should allow for easy expansion to support additional formats and advanced options in future phases.
