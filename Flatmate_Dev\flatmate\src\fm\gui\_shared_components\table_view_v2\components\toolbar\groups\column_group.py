"""
Column Group

Handles column visibility and management functionality.
Contains column visibility button and related column management features.
"""

from PySide6.QtCore import Signal, QSize
from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton
from PySide6.QtGui import QIcon


class ColumnVisibilityButton(QPushButton):
    """Button for showing column visibility menu with eye icon."""

    visibility_requested = Signal()  # Emitted when button is clicked

    def __init__(self, text="", parent=None):
        """Initialize the column visibility button with eye icon only."""
        super().__init__(text, parent)
        self.clicked.connect(self.visibility_requested)
        self.setToolTip("Show/Hide Columns")

        # Load eye icon from navigation bar
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer

            # Use the same eye icon as the navigation bar
            eye_icon_path = icon_manager.get_nav_icon("view_data")
            eye_icon = IconRenderer.load_icon(eye_icon_path, QSize(16, 16))
            self.setIcon(eye_icon)
            self.setIconSize(QSize(16, 16))

        except Exception as e:
            print(f"Warning: Could not load eye icon: {e}")

        # Apply consistent styling for icon-only button
        self.setStyleSheet("""
            QPushButton {
                background-color: #2A2A2A;
                color: white;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 6px;
                min-width: 32px;
                max-width: 32px;
                min-height: 32px;
                max-height: 32px;
            }
            QPushButton:hover {
                background-color: #3A3A3A;
                border-color: #3B8A45;
            }
            QPushButton:pressed {
                background-color: #1A1A1A;
            }
        """)


class ColumnGroup(QWidget):
    """Group focused solely on column management functionality."""
    
    # Signals for external communication
    column_visibility_requested = Signal()
    
    def __init__(self, parent=None):
        """Initialize the column group."""
        super().__init__(parent)

        # Set CSS class for styling
        self.setObjectName("ColumnGroup")

        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)  # Reduced from 5px to 4px for tighter spacing
        
        # Column visibility button
        self.column_visibility_button = ColumnVisibilityButton()
        layout.addWidget(self.column_visibility_button)
    
    def _connect_signals(self):
        """Connect internal signals."""
        # Forward signals from column visibility button
        self.column_visibility_button.visibility_requested.connect(
            self.column_visibility_requested)
