"""
Search Query Processor - Specialized Implementation

Search-specific implementation of the hybrid query processor pattern.
This is the reusable version of the search functionality, extracted from
the table view implementation.
"""

import logging
from typing import Optional
from functools import lru_cache

from .hybrid_query_processor import HybridQueryProcessor, SimpleQueryProcessor, ComplexQueryProcessor

# Optional luqum import - graceful degradation if not available
try:
    from luqum import parser as luqum_parser
    from luqum.tree import SearchField, Word, Phrase, Group, AndOperation, OrOperation, Not
    LUQUM_AVAILABLE = True
except ImportError:
    LUQUM_AVAILABLE = False
    luqum_parser = None

logger = logging.getLogger(__name__)


class SearchQueryPreprocessor:
    """Preprocesses user search queries for package-based parsing."""
    
    def preprocess(self, user_query: str) -> str:
        """Convert user-friendly query to Lucene syntax.
        
        Args:
            user_query: User input query string
            
        Returns:
            Normalized query string for luqum parsing
        """
        if not user_query or not user_query.strip():
            return ""
        
        # Store quoted strings to protect them from processing
        quoted_parts = []
        query = self._extract_quoted_strings(user_query, quoted_parts)
        
        # Convert operator synonyms
        query = self._convert_or_operators(query)
        query = self._convert_not_operators(query)
        query = self._normalize_whitespace(query)
        
        # Restore quoted strings
        query = self._restore_quoted_strings(query, quoted_parts)
        
        return query.strip()
    
    def _extract_quoted_strings(self, query: str, storage: list) -> str:
        """Extract quoted strings to protect them from processing."""
        import re
        pattern = r'"([^"]*)"'
        
        def replace_quote(match):
            storage.append(match.group(0))
            return f"__QUOTE_{len(storage)-1}__"
        
        return re.sub(pattern, replace_quote, query)
    
    def _convert_or_operators(self, query: str) -> str:
        """Convert | and / to OR."""
        import re
        # Handle word|word and word/word patterns
        query = re.sub(r'(\w+)\|(\w+)', r'\1 OR \2', query)
        query = re.sub(r'(\w+)/(\w+)', r'\1 OR \2', query)
        
        # Handle more complex patterns like (word1|word2)
        query = re.sub(r'\|', ' OR ', query)
        query = re.sub(r'/', ' OR ', query)
        
        return query
    
    def _convert_not_operators(self, query: str) -> str:
        """Convert -term to NOT term."""
        import re
        # Handle -word patterns (but not standalone -)
        query = re.sub(r'\s-(\w+)', r' NOT \1', query)
        query = re.sub(r'^-(\w+)', r'NOT \1', query)  # Handle start of string
        return query
    
    def _normalize_whitespace(self, query: str) -> str:
        """Normalize whitespace and clean up the query."""
        import re
        # Replace multiple spaces with single space
        query = re.sub(r'\s+', ' ', query)
        
        # Clean up spaces around operators
        query = re.sub(r'\s*OR\s*', ' OR ', query)
        query = re.sub(r'\s*AND\s*', ' AND ', query)
        query = re.sub(r'\s*NOT\s*', ' NOT ', query)
        
        return query.strip()
    
    def _restore_quoted_strings(self, query: str, storage: list) -> str:
        """Restore quoted strings."""
        for i, quoted in enumerate(storage):
            query = query.replace(f"__QUOTE_{i}__", quoted)
        return query


class SearchExpressionEvaluator:
    """Evaluates parsed search expressions against data text."""
    
    def __init__(self, data_text: str):
        """Initialize evaluator with data text."""
        self.data_text = data_text.lower()
    
    def evaluate(self, node) -> bool:
        """Evaluate AST node against data text."""
        if isinstance(node, Word):
            return self._evaluate_word(node)
        elif isinstance(node, Phrase):
            return self._evaluate_phrase(node)
        elif isinstance(node, AndOperation):
            return self._evaluate_and(node)
        elif isinstance(node, OrOperation):
            return self._evaluate_or(node)
        elif isinstance(node, Not):
            return self._evaluate_not(node)
        elif isinstance(node, Group):
            return self.evaluate(node.children[0])
        else:
            # For unknown node types, try to evaluate children
            if hasattr(node, 'children') and node.children:
                # Default to AND behavior for unknown operations
                return all(self.evaluate(child) for child in node.children)
            return True
    
    def _evaluate_word(self, node) -> bool:
        """Evaluate a word node."""
        word = str(node.value).lower()
        return word in self.data_text
    
    def _evaluate_phrase(self, node) -> bool:
        """Evaluate a phrase node (quoted string)."""
        phrase = str(node.value).lower().strip('"')
        return phrase in self.data_text
    
    def _evaluate_and(self, node) -> bool:
        """Evaluate an AND operation."""
        return all(self.evaluate(child) for child in node.children)
    
    def _evaluate_or(self, node) -> bool:
        """Evaluate an OR operation."""
        return any(self.evaluate(child) for child in node.children)
    
    def _evaluate_not(self, node) -> bool:
        """Evaluate a NOT operation."""
        if node.children:
            return not self.evaluate(node.children[0])
        return True


class LuqumSearchProcessor(ComplexQueryProcessor):
    """Complex processor using luqum package for boolean search."""
    
    def __init__(self):
        """Initialize luqum processor."""
        self.preprocessor = SearchQueryPreprocessor()
        self._parser_available = LUQUM_AVAILABLE
        
        if not self._parser_available:
            logger.warning("luqum package not available, complex search functionality will be limited")
    
    @lru_cache(maxsize=128)
    def _cached_parse(self, normalized_query: str):
        """Cache parsed queries for better performance."""
        if not self._parser_available:
            return None
        
        try:
            return luqum_parser.parse(normalized_query)
        except Exception as e:
            raise ValueError(f"Failed to parse normalized query '{normalized_query}': {e}")
    
    def process(self, query: str, data) -> bool:
        """Process query using luqum parser."""
        if not self._parser_available:
            # Fallback to simple string matching
            return self._fallback_process(query, data)
        
        if not query or not query.strip():
            return True
        
        try:
            # Preprocess user input
            normalized_query = self.preprocessor.preprocess(query)
            
            if not normalized_query:
                return True
            
            # Parse with luqum (cached)
            ast = self._cached_parse(normalized_query)
            if ast is None:
                return True
            
            # Evaluate against data
            evaluator = SearchExpressionEvaluator(str(data))
            return evaluator.evaluate(ast)
        
        except Exception as e:
            logger.warning(f"Luqum processing failed for '{query}': {e}, falling back to simple matching")
            return self._fallback_process(query, data)
    
    def _fallback_process(self, query: str, data) -> bool:
        """Fallback processing using simple string matching."""
        if not query or not query.strip():
            return True
        
        query_lower = query.lower()
        data_str = str(data).lower()
        
        # Very basic AND logic (space-separated terms)
        terms = query_lower.split()
        for term in terms:
            if term.startswith('-') and len(term) > 1:
                # Exclude term
                if term[1:] in data_str:
                    return False
            else:
                # Include term
                if term not in data_str:
                    return False
        
        return True


class SearchStringProcessor(SimpleQueryProcessor):
    """Simple processor optimized for search queries."""
    
    def process(self, query: str, data) -> bool:
        """Process search query using fast string matching."""
        if not query or not query.strip():
            return True
        
        query_lower = query.lower().strip()
        data_str = str(data).lower()
        
        # Split into terms
        terms = query_lower.split()
        
        # Process each term
        for term in terms:
            if term.startswith('-') and len(term) > 1:
                # Exclusion term
                exclude_term = term[1:]
                if exclude_term in data_str:
                    return False
            else:
                # Include term (must be present)
                if term not in data_str:
                    return False
        
        return True


def create_search_query_processor() -> HybridQueryProcessor:
    """Create a hybrid processor optimized for search queries.
    
    Returns:
        Configured HybridQueryProcessor for search operations
    """
    simple_proc = SearchStringProcessor()
    complex_proc = LuqumSearchProcessor()
    
    # Search-specific complexity indicators
    complexity_indicators = [
        'OR', 'AND', 'NOT', '|', '/', '(', ')', '"'
    ]
    
    processor = HybridQueryProcessor(
        simple_processor=simple_proc,
        complex_processor=complex_proc,
        complexity_indicators=complexity_indicators
    )
    
    return processor


# Singleton instance for global use
_search_processor_instance = None

def get_search_query_processor() -> HybridQueryProcessor:
    """Get the global search query processor instance."""
    global _search_processor_instance
    if _search_processor_instance is None:
        _search_processor_instance = create_search_query_processor()
    return _search_processor_instance
