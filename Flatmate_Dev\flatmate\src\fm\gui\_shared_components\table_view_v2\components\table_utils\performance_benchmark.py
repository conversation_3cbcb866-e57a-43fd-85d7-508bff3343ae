"""
Performance benchmark suite for package-based search implementation.

Comprehensive performance testing to validate that the new implementation
meets performance targets and identify optimization opportunities.
"""

import time
import statistics
import gc
import psutil
import os
from typing import List, Dict, Tuple

try:
    from search_query_parser import SearchQueryParser, SearchQueryPreprocessor
    from enhanced_filter_proxy_model import EnhancedFilterProxyModel
except ImportError as e:
    print(f"Import error: {e}")
    exit(1)


class PerformanceBenchmark:
    """Comprehensive performance benchmark suite."""
    
    def __init__(self):
        self.parser = SearchQueryParser()
        self.preprocessor = SearchQueryPreprocessor()
        self.proxy_model = EnhancedFilterProxyModel()
        self.results = {}
        
        # Test data
        self.test_data = self._generate_test_data()
        self.test_queries = self._generate_test_queries()
    
    def _generate_test_data(self) -> List[str]:
        """Generate realistic test data for benchmarking."""
        return [
            "Starbucks coffee shop purchase downtown",
            "Tea house visit with friends",
            "Decaf coffee from local cafe",
            "Regular coffee and tea combo",
            "Gas station snacks and drinks",
            "Restaurant dinner with wine",
            "Grocery store weekly shopping",
            "Online purchase electronics",
            "Bank transfer to savings",
            "ATM withdrawal cash",
            "Coffee shop morning latte",
            "Tea ceremony traditional",
            "Decaf espresso afternoon",
            "Regular americano black",
            "Gas station fuel fill",
            "Restaurant lunch special",
            "Grocery organic produce",
            "Online subscription service",
            "Bank loan payment",
            "ATM deposit check",
        ] * 50  # 1000 items total
    
    def _generate_test_queries(self) -> Dict[str, List[str]]:
        """Generate test queries of different complexity levels."""
        return {
            "simple": [
                "coffee",
                "tea",
                "gas",
                "restaurant",
                "bank",
            ],
            "and_operations": [
                "coffee shop",
                "tea house",
                "gas station",
                "restaurant dinner",
                "bank transfer",
            ],
            "or_operations": [
                "coffee OR tea",
                "restaurant OR grocery",
                "bank OR atm",
                "online OR purchase",
                "gas OR fuel",
            ],
            "not_operations": [
                "coffee -decaf",
                "restaurant -lunch",
                "bank -transfer",
                "online -subscription",
                "gas -snacks",
            ],
            "complex": [
                "(coffee OR tea) -decaf",
                "(restaurant OR grocery) AND (dinner OR lunch)",
                "bank AND (transfer OR payment) -loan",
                "(online OR purchase) AND electronics -subscription",
                "(gas OR fuel) AND station -snacks",
            ],
            "phrases": [
                '"coffee shop"',
                '"tea house"',
                '"gas station"',
                '"restaurant dinner"',
                '"bank transfer"',
            ]
        }
    
    def run_all_benchmarks(self):
        """Run all performance benchmarks."""
        print("Package-Based Search Performance Benchmark")
        print("=" * 60)
        
        # Memory baseline
        self._measure_memory_baseline()
        
        # Core performance tests
        self._benchmark_parsing()
        self._benchmark_evaluation()
        self._benchmark_integration()
        self._benchmark_memory_usage()
        self._benchmark_initialization()
        
        # Comparison tests
        self._benchmark_vs_legacy()
        
        # Stress tests
        self._benchmark_stress_test()
        
        # Generate report
        self._generate_report()
    
    def _measure_memory_baseline(self):
        """Measure baseline memory usage."""
        gc.collect()
        process = psutil.Process(os.getpid())
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        self.results['memory_baseline'] = baseline_memory
        print(f"Memory baseline: {baseline_memory:.1f} MB")
    
    def _benchmark_parsing(self):
        """Benchmark query parsing performance."""
        print("\n1. Parsing Performance")
        print("-" * 30)
        
        parsing_results = {}
        
        for category, queries in self.test_queries.items():
            times = []
            
            for query in queries:
                # Warm up
                for _ in range(5):
                    try:
                        self.parser.parse(query)
                    except:
                        pass
                
                # Benchmark
                start_time = time.perf_counter()
                for _ in range(100):
                    try:
                        self.parser.parse(query)
                    except:
                        pass
                end_time = time.perf_counter()
                
                avg_time = (end_time - start_time) * 1000 / 100  # ms
                times.append(avg_time)
            
            avg_time = statistics.mean(times)
            p95_time = statistics.quantiles(times, n=20)[18]  # 95th percentile
            
            parsing_results[category] = {
                'avg_ms': avg_time,
                'p95_ms': p95_time,
                'samples': len(times)
            }
            
            status = "✅" if avg_time < 10 else "⚠️" if avg_time < 50 else "❌"
            print(f"  {status} {category:15} | Avg: {avg_time:.2f}ms | P95: {p95_time:.2f}ms")
        
        self.results['parsing'] = parsing_results
    
    def _benchmark_evaluation(self):
        """Benchmark query evaluation performance."""
        print("\n2. Evaluation Performance")
        print("-" * 30)
        
        evaluation_results = {}
        
        for category, queries in self.test_queries.items():
            times = []
            
            for query in queries:
                # Test against multiple data items
                query_times = []
                
                for data_item in self.test_data[:100]:  # Test against 100 items
                    start_time = time.perf_counter()
                    try:
                        self.parser.evaluate(query, data_item)
                    except:
                        pass
                    end_time = time.perf_counter()
                    
                    query_times.append((end_time - start_time) * 1000)
                
                times.extend(query_times)
            
            avg_time = statistics.mean(times)
            p95_time = statistics.quantiles(times, n=20)[18]  # 95th percentile
            
            evaluation_results[category] = {
                'avg_ms': avg_time,
                'p95_ms': p95_time,
                'samples': len(times)
            }
            
            status = "✅" if avg_time < 5 else "⚠️" if avg_time < 20 else "❌"
            print(f"  {status} {category:15} | Avg: {avg_time:.2f}ms | P95: {p95_time:.2f}ms")
        
        self.results['evaluation'] = evaluation_results
    
    def _benchmark_integration(self):
        """Benchmark integration with filter proxy model."""
        print("\n3. Integration Performance")
        print("-" * 30)
        
        times = []
        
        for query in self.test_queries['complex']:
            for data_item in self.test_data[:50]:
                start_time = time.perf_counter()
                try:
                    self.proxy_model._check_pattern_match(data_item, query)
                except:
                    pass
                end_time = time.perf_counter()
                
                times.append((end_time - start_time) * 1000)
        
        avg_time = statistics.mean(times)
        p95_time = statistics.quantiles(times, n=20)[18]
        
        self.results['integration'] = {
            'avg_ms': avg_time,
            'p95_ms': p95_time,
            'samples': len(times)
        }
        
        status = "✅" if avg_time < 10 else "⚠️" if avg_time < 50 else "❌"
        print(f"  {status} Integration     | Avg: {avg_time:.2f}ms | P95: {p95_time:.2f}ms")
    
    def _benchmark_memory_usage(self):
        """Benchmark memory usage."""
        print("\n4. Memory Usage")
        print("-" * 30)
        
        gc.collect()
        process = psutil.Process(os.getpid())
        
        # Memory after initialization
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        overhead = current_memory - self.results['memory_baseline']
        
        # Memory during heavy usage
        for _ in range(1000):
            query = "coffee OR tea AND NOT decaf"
            try:
                ast = self.parser.parse(query)
                self.parser.evaluate(query, "coffee shop with tea")
            except:
                pass
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        peak_overhead = peak_memory - self.results['memory_baseline']
        
        self.results['memory'] = {
            'baseline_mb': self.results['memory_baseline'],
            'current_mb': current_memory,
            'peak_mb': peak_memory,
            'overhead_mb': overhead,
            'peak_overhead_mb': peak_overhead
        }
        
        status = "✅" if overhead < 10 else "⚠️" if overhead < 50 else "❌"
        print(f"  {status} Memory overhead: {overhead:.1f} MB")
        print(f"  {status} Peak overhead:   {peak_overhead:.1f} MB")
    
    def _benchmark_initialization(self):
        """Benchmark initialization performance."""
        print("\n5. Initialization Performance")
        print("-" * 30)
        
        # Time to create new parser instance
        times = []
        for _ in range(10):
            start_time = time.perf_counter()
            parser = SearchQueryParser()
            end_time = time.perf_counter()
            times.append((end_time - start_time) * 1000)
        
        avg_init_time = statistics.mean(times)
        
        # Time for first parse (JIT compilation)
        start_time = time.perf_counter()
        parser.parse("coffee OR tea")
        end_time = time.perf_counter()
        first_parse_time = (end_time - start_time) * 1000
        
        self.results['initialization'] = {
            'avg_init_ms': avg_init_time,
            'first_parse_ms': first_parse_time
        }
        
        status = "✅" if avg_init_time < 100 else "⚠️" if avg_init_time < 500 else "❌"
        print(f"  {status} Initialization: {avg_init_time:.1f}ms")
        print(f"  {status} First parse:    {first_parse_time:.1f}ms")
    
    def _benchmark_vs_legacy(self):
        """Compare performance with legacy implementation."""
        print("\n6. Legacy Comparison")
        print("-" * 30)
        
        # Test legacy methods
        legacy_times = []
        package_times = []
        
        test_queries = ["coffee shop", "restaurant -lunch", "bank transfer"]
        
        for query in test_queries:
            for data_item in self.test_data[:100]:
                # Legacy method
                start_time = time.perf_counter()
                try:
                    self.proxy_model._legacy_check_pattern_match(data_item, query)
                except:
                    pass
                end_time = time.perf_counter()
                legacy_times.append((end_time - start_time) * 1000)
                
                # Package method
                start_time = time.perf_counter()
                try:
                    self.parser.evaluate(query, data_item)
                except:
                    pass
                end_time = time.perf_counter()
                package_times.append((end_time - start_time) * 1000)
        
        legacy_avg = statistics.mean(legacy_times)
        package_avg = statistics.mean(package_times)
        improvement = ((legacy_avg - package_avg) / legacy_avg) * 100
        
        self.results['comparison'] = {
            'legacy_avg_ms': legacy_avg,
            'package_avg_ms': package_avg,
            'improvement_pct': improvement
        }
        
        status = "✅" if improvement > 0 else "⚠️"
        print(f"  {status} Legacy avg:     {legacy_avg:.2f}ms")
        print(f"  {status} Package avg:    {package_avg:.2f}ms")
        print(f"  {status} Improvement:    {improvement:+.1f}%")
    
    def _benchmark_stress_test(self):
        """Stress test with large datasets."""
        print("\n7. Stress Test")
        print("-" * 30)
        
        # Large dataset test
        large_dataset = self.test_data * 10  # 10,000 items
        complex_query = "(coffee OR tea) AND shop -decaf"
        
        start_time = time.perf_counter()
        matches = 0
        for item in large_dataset:
            try:
                if self.parser.evaluate(complex_query, item):
                    matches += 1
            except:
                pass
        end_time = time.perf_counter()
        
        total_time = (end_time - start_time) * 1000
        per_item_time = total_time / len(large_dataset)
        
        self.results['stress_test'] = {
            'dataset_size': len(large_dataset),
            'total_time_ms': total_time,
            'per_item_ms': per_item_time,
            'matches': matches
        }
        
        status = "✅" if per_item_time < 1 else "⚠️" if per_item_time < 5 else "❌"
        print(f"  {status} Dataset size:   {len(large_dataset):,} items")
        print(f"  {status} Total time:     {total_time:.0f}ms")
        print(f"  {status} Per item:       {per_item_time:.3f}ms")
        print(f"  {status} Matches found:  {matches:,}")
    
    def _generate_report(self):
        """Generate comprehensive performance report."""
        print("\n" + "=" * 60)
        print("PERFORMANCE SUMMARY")
        print("=" * 60)
        
        # Overall assessment
        parsing_avg = statistics.mean([r['avg_ms'] for r in self.results['parsing'].values()])
        eval_avg = statistics.mean([r['avg_ms'] for r in self.results['evaluation'].values()])
        
        print(f"\n📊 Key Metrics:")
        print(f"  • Average parse time:     {parsing_avg:.2f}ms")
        print(f"  • Average evaluation:     {eval_avg:.2f}ms")
        print(f"  • Memory overhead:        {self.results['memory']['overhead_mb']:.1f}MB")
        print(f"  • Initialization time:    {self.results['initialization']['avg_init_ms']:.1f}ms")
        
        # Performance grades
        parse_grade = "A+" if parsing_avg < 1 else "A" if parsing_avg < 5 else "B" if parsing_avg < 10 else "C"
        eval_grade = "A+" if eval_avg < 1 else "A" if eval_avg < 3 else "B" if eval_avg < 5 else "C"
        memory_grade = "A" if self.results['memory']['overhead_mb'] < 5 else "B" if self.results['memory']['overhead_mb'] < 10 else "C"
        
        print(f"\n🎯 Performance Grades:")
        print(f"  • Parsing Performance:    {parse_grade}")
        print(f"  • Evaluation Performance: {eval_grade}")
        print(f"  • Memory Efficiency:      {memory_grade}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        if parsing_avg > 5:
            print("  • Consider query caching for repeated patterns")
        if eval_avg > 3:
            print("  • Consider lazy evaluation for complex queries")
        if self.results['memory']['overhead_mb'] > 10:
            print("  • Monitor memory usage in production")
        
        overall_grade = min(parse_grade, eval_grade, memory_grade, key=lambda x: ord(x[0]))
        print(f"\n🏆 Overall Performance Grade: {overall_grade}")
        
        if overall_grade in ['A+', 'A']:
            print("🎉 Excellent performance! Ready for production deployment.")
        elif overall_grade == 'B':
            print("✅ Good performance. Consider minor optimizations.")
        else:
            print("⚠️  Performance needs improvement before production.")


def main():
    """Run the performance benchmark suite."""
    benchmark = PerformanceBenchmark()
    benchmark.run_all_benchmarks()


if __name__ == "__main__":
    main()
