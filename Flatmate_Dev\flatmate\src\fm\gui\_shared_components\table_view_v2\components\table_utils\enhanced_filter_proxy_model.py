"""
Enhanced Filter Proxy Model

Filtering and sorting component for the Enhanced Table View System.
Provides per-column filtering and advanced search capabilities with package-based parsing.
"""

from PySide6.QtCore import Qt, QSortFilterProxyModel

try:
    from .search_query_parser import get_search_parser
except ImportError:
    # Handle direct execution or testing scenarios
    from search_query_parser import get_search_parser


class EnhancedFilterProxyModel(QSortFilterProxyModel):
    """Enhanced filter proxy model with per-column filtering."""
    
    def __init__(self, parent=None):
        """Initialize the enhanced filter proxy model."""
        super().__init__(parent)
        self._column_filters = {}
        self._search_parser = get_search_parser()
        self._use_package_parser = True  # Feature flag for package-based parsing
    
    def set_column_filter(self, column, pattern: str):
        """Set filter for a specific column or all columns."""
        if not pattern:
            if column in self._column_filters:
                del self._column_filters[column]
        else:
            self._column_filters[column] = pattern
        self.invalidateFilter()
    
    def clear_filters(self):
        """Clear all filters."""
        self._column_filters.clear()
        self.invalidateFilter()
    
    def filterAcceptsRow(self, source_row, source_parent):
        """Check if row matches all column filters."""
        model = self.sourceModel()

        # If no filters, accept all rows
        if not self._column_filters:
            return True

        # Check each column filter
        for column, pattern in self._column_filters.items():
            # Single column search
                index = model.index(source_row, column, source_parent)
                if not index.isValid():
                    if source_row == 0:
                        print(f"DEBUG: Invalid index for column {column}")
                    continue

                data = model.data(index, Qt.DisplayRole)
                if data is None:
                    if source_row == 0:
                        print(f"DEBUG: No data for column {column}")
                    continue

                if not self._check_pattern_match(str(data), pattern):
                    return False

        return True

    def _parse_filter_pattern(self, pattern: str) -> tuple[list[str], list[str]]:
        """Parse pattern into AND terms and EXCLUDE terms.

        Args:
            pattern: Filter pattern (e.g., "foo bar -exclude")

        Returns:
            Tuple of (and_terms, exclude_terms)
        """
        if not pattern.strip():
            return [], []

        terms = pattern.split()
        and_terms = []
        exclude_terms = []

        for term in terms:
            if term.startswith('-') and len(term) > 1:
                # Exclude term (remove the -)
                exclude_terms.append(term[1:].lower())
            else:
                # AND term (including terms that are just "-")
                and_terms.append(term.lower())

        return and_terms, exclude_terms

    def _parse_filter_pattern_v2(self, pattern: str) -> dict:
        """Enhanced pattern parsing with OR and basic grouping support.

        Args:
            pattern: Filter pattern (e.g., "coffee|tea -decaf" or "(coffee|tea) -decaf")

        Returns:
            Dict with parsed expression structure
        """
        if not pattern.strip():
            return {"type": "empty"}

        # Check if pattern contains parentheses (basic grouping)
        if '(' in pattern and ')' in pattern:
            return self._parse_grouped_expression(pattern)
        # Check if pattern contains OR operators
        elif '|' in pattern:
            return self._parse_or_expression(pattern)
        else:
            # Fall back to original AND/exclude logic
            and_terms, exclude_terms = self._parse_filter_pattern(pattern)
            return {
                "type": "and_exclude",
                "and_terms": and_terms,
                "exclude_terms": exclude_terms
            }

    def _parse_or_expression(self, pattern: str) -> dict:
        """Parse pattern with OR operators.

        Args:
            pattern: Pattern like "coffee|tea -decaf" or "coffee|tea hot"

        Returns:
            Dict with OR expression structure
        """
        # Split by spaces to handle exclude terms and separate OR groups
        parts = pattern.split()
        or_groups = []
        exclude_terms = []

        for part in parts:
            if part.startswith('-') and len(part) > 1:
                # Exclude term - handle pipes in exclude terms properly
                exclude_part = part[1:]  # Remove the -
                if '|' in exclude_part:
                    # Multiple exclude terms: -term1|term2 means exclude term1 OR term2
                    exclude_list = [term.strip().lower() for term in exclude_part.split('|') if term.strip()]
                    exclude_terms.extend(exclude_list)
                else:
                    exclude_terms.append(exclude_part.lower())
            else:
                # OR group - split by pipe
                if '|' in part:
                    or_terms = [term.strip().lower() for term in part.split('|') if term.strip()]
                    or_groups.append(or_terms)
                else:
                    # Single term (treat as OR group with one item)
                    or_groups.append([part.lower()])

        return {
            "type": "or_expression",
            "or_groups": or_groups,
            "exclude_terms": exclude_terms
        }

    def _parse_grouped_expression(self, pattern: str) -> dict:
        """Parse pattern with basic parentheses grouping.

        Args:
            pattern: Pattern like "(coffee|tea) -decaf" or "(coffee|tea) hot"

        Returns:
            Dict with grouped expression structure
        """
        # Simple implementation for basic grouping
        # Find the first parentheses group
        start = pattern.find('(')
        end = pattern.find(')', start)

        if start == -1 or end == -1 or end <= start:
            # Invalid parentheses, fall back to OR parsing
            return self._parse_or_expression(pattern)

        # Extract the grouped part and the rest
        grouped_part = pattern[start+1:end]
        before_group = pattern[:start].strip()
        after_group = pattern[end+1:].strip()

        # Combine before and after parts
        remaining_parts = []
        if before_group:
            remaining_parts.append(before_group)
        if after_group:
            remaining_parts.append(after_group)

        remaining_pattern = ' '.join(remaining_parts)

        # Parse the grouped part as OR expression
        grouped_parsed = self._parse_or_expression(grouped_part)

        # Parse the remaining parts
        if remaining_pattern:
            remaining_parsed = self._parse_filter_pattern_v2(remaining_pattern)
        else:
            remaining_parsed = {"type": "empty"}

        return {
            "type": "grouped_expression",
            "grouped_part": grouped_parsed,
            "remaining_part": remaining_parsed
        }

    def _check_pattern_match(self, data_str: str, pattern: str) -> bool:
        """Check if data matches the filter pattern using package-based parsing.

        Includes performance safeguards and error handling to prevent UI freezes.
        """
        # Performance safeguard: Skip empty patterns
        if not pattern or not pattern.strip():
            return True

        # Performance safeguard: Skip very long patterns that might cause issues
        if len(pattern) > 500:  # Reasonable limit for search patterns
            print(f"WARNING: Pattern too long ({len(pattern)} chars), skipping: {pattern[:50]}...")
            return True

        if self._use_package_parser and self._search_parser.is_available():
            # Use new package-based parser with error handling
            try:
                return self._search_parser.evaluate(pattern, data_str)
            except Exception as e:
                # Fallback to legacy parsing on error, but log the issue
                print(f"WARNING: Package parser failed for '{pattern}': {e}, falling back to legacy")
                try:
                    return self._legacy_check_pattern_match(data_str, pattern)
                except Exception as e2:
                    # If both parsers fail, log error and return True to avoid hiding all data
                    print(f"ERROR: Both parsers failed for '{pattern}': {e2}, returning True")
                    return True
        else:
            # Use legacy parsing with error handling
            try:
                return self._legacy_check_pattern_match(data_str, pattern)
            except Exception as e:
                # If parsing fails, log error and return True to avoid hiding all data
                print(f"ERROR: Legacy parser failed for '{pattern}': {e}, returning True")
                return True

    def _legacy_check_pattern_match(self, data_str: str, pattern: str) -> bool:
        """Legacy pattern matching using the original custom parser."""
        # Use enhanced parsing
        parsed = self._parse_filter_pattern_v2(pattern)
        data_lower = data_str.lower()

        # Use the helper method for evaluation
        return self._evaluate_parsed_expression(data_lower, parsed)

    def _evaluate_parsed_expression(self, data_lower: str, parsed: dict) -> bool:
        """Evaluate a parsed expression against data.

        Args:
            data_lower: Lowercase data string to match against
            parsed: Parsed expression dictionary

        Returns:
            True if expression matches the data
        """
        if parsed["type"] == "empty":
            return True

        elif parsed["type"] == "and_exclude":
            # Original AND/exclude logic
            and_terms = parsed["and_terms"]
            exclude_terms = parsed["exclude_terms"]

            # All AND terms must be present
            for term in and_terms:
                if term not in data_lower:
                    return False

            # No EXCLUDE terms may be present
            for term in exclude_terms:
                if term in data_lower:
                    return False

            return True

        elif parsed["type"] == "or_expression":
            # Enhanced OR logic
            or_groups = parsed["or_groups"]
            exclude_terms = parsed["exclude_terms"]

            # Check exclude terms first (if any exclude term matches, reject)
            for term in exclude_terms:
                if term in data_lower:
                    return False

            # All OR groups must have at least one matching term
            for or_group in or_groups:
                group_matched = False
                for term in or_group:
                    if term in data_lower:
                        group_matched = True
                        break

                # If this OR group didn't match, reject the row
                if not group_matched:
                    return False

            return True

        elif parsed["type"] == "grouped_expression":
            # Handle grouped expressions recursively
            grouped_part = parsed["grouped_part"]
            remaining_part = parsed["remaining_part"]

            # Both parts must match
            grouped_result = self._evaluate_parsed_expression(data_lower, grouped_part)
            if not grouped_result:
                return False

            if remaining_part["type"] != "empty":
                remaining_result = self._evaluate_parsed_expression(data_lower, remaining_part)
                if not remaining_result:
                    return False

            return True

        # Fallback
        return True

    def lessThan(self, left, right):
        """Custom sorting to use original data values instead of display text."""
        # Get original data from UserRole for proper sorting
        left_data = self.sourceModel().data(left, Qt.UserRole)
        right_data = self.sourceModel().data(right, Qt.UserRole)

        # If we have original data, use it for comparison
        if left_data is not None and right_data is not None:
            # Handle None values (put them at the end)
            if left_data is None and right_data is not None:
                return False
            if left_data is not None and right_data is None:
                return True
            if left_data is None and right_data is None:
                return False

            # For dates and other comparable types, use direct comparison
            try:
                return left_data < right_data
            except TypeError:
                # Fallback to string comparison if types aren't comparable
                return str(left_data) < str(right_data)

        # Fallback to default behavior if no UserRole data
        return super().lessThan(left, right)


