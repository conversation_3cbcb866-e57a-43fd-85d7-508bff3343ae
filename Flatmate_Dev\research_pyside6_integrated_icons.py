"""
PySide6 Integrated Icons Research

This script tests the capabilities of PySide6 for embedding icons/buttons
inside QLineEdit widgets, specifically for the apply button integration.

Research Areas:
1. QLineEdit.addAction() method capabilities
2. Icon positioning and styling options
3. Signal/slot connectivity
4. Limitations and alternatives
"""

import sys
from PySide6.QtWidgets import (QApplication, QLineEdit, QVBoxLayout, QWidget, 
                               QLabel, QHBoxLayout, QPushButton, QToolButton)
from PySide6.QtGui import QAction, QIcon
from PySide6.QtCore import QSize, Signal
from pathlib import Path


class IntegratedIconResearch(QWidget):
    """Research widget to test PySide6 integrated icon approaches."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PySide6 Integrated Icon Research")
        self.setGeometry(100, 100, 600, 400)

        layout = QVBoxLayout(self)

        # Results display (create first)
        self.results_label = QLabel("Starting research tests...")
        layout.addWidget(self.results_label)

        # Test 1: QLineEdit.addAction() method
        layout.addWidget(QLabel("Test 1: QLineEdit.addAction() - Trailing Position"))
        self.test_addaction_trailing()
        layout.addWidget(self.line_edit_1)

        # Test 2: QLineEdit.addAction() - Leading Position
        layout.addWidget(QLabel("Test 2: QLineEdit.addAction() - Leading Position"))
        self.test_addaction_leading()
        layout.addWidget(self.line_edit_2)

        # Test 3: Custom composite widget approach
        layout.addWidget(QLabel("Test 3: Custom Composite Widget"))
        self.test_composite_widget()
        layout.addWidget(self.composite_widget)

        # Test 4: Overlay approach
        layout.addWidget(QLabel("Test 4: Overlay Button Approach"))
        self.test_overlay_approach()
        layout.addWidget(self.overlay_widget)
    
    def test_addaction_trailing(self):
        """Test QLineEdit.addAction() with trailing position."""
        self.line_edit_1 = QLineEdit()
        self.line_edit_1.setPlaceholderText("Type here... (apply button on right)")
        
        # Create icon (using a simple text icon for testing)
        try:
            # Try to load actual icon
            icon_path = Path("flatmate/src/fm/gui/icons/toolbar/check/check.svg")
            if icon_path.exists():
                apply_icon = QIcon(str(icon_path))
            else:
                # Fallback to system icon or create simple one
                apply_icon = self.line_edit_1.style().standardIcon(
                    self.line_edit_1.style().SP_DialogApplyButton
                )
        except:
            # Ultimate fallback
            apply_icon = QIcon()
        
        # Add trailing action (right side)
        if hasattr(self.line_edit_1, 'addAction'):
            apply_action = self.line_edit_1.addAction(apply_icon, QLineEdit.TrailingPosition)
            apply_action.triggered.connect(lambda: self.log_result("addAction trailing clicked"))
            self.log_result("✅ addAction method available - trailing position added")
        else:
            self.log_result("❌ addAction method not available")
    
    def test_addaction_leading(self):
        """Test QLineEdit.addAction() with leading position."""
        self.line_edit_2 = QLineEdit()
        self.line_edit_2.setPlaceholderText("Type here... (search icon on left)")
        
        # Create search icon
        try:
            icon_path = Path("flatmate/src/fm/gui/icons/toolbar/search/search.svg")
            if icon_path.exists():
                search_icon = QIcon(str(icon_path))
            else:
                search_icon = self.line_edit_2.style().standardIcon(
                    self.line_edit_2.style().SP_FileDialogDetailedView
                )
        except:
            search_icon = QIcon()
        
        # Add leading action (left side)
        if hasattr(self.line_edit_2, 'addAction'):
            search_action = self.line_edit_2.addAction(search_icon, QLineEdit.LeadingPosition)
            search_action.triggered.connect(lambda: self.log_result("addAction leading clicked"))
            self.log_result("✅ addAction method available - leading position added")
        else:
            self.log_result("❌ addAction method not available")
    
    def test_composite_widget(self):
        """Test custom composite widget approach."""
        self.composite_widget = QWidget()
        layout = QHBoxLayout(self.composite_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Container with border to simulate line edit appearance
        self.composite_widget.setStyleSheet("""
            QWidget {
                border: 1px solid #333333;
                border-radius: 4px;
                background-color: #1E1E1E;
            }
        """)
        
        # Line edit without border
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("Custom composite widget...")
        line_edit.setStyleSheet("""
            QLineEdit {
                border: none;
                background: transparent;
                color: white;
                padding: 4px;
            }
        """)
        
        # Apply button
        apply_btn = QPushButton("✓")
        apply_btn.setFixedSize(24, 24)
        apply_btn.clicked.connect(lambda: self.log_result("Composite apply button clicked"))
        apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B8A45;
                color: white;
                border: none;
                border-radius: 2px;
            }
        """)
        
        layout.addWidget(line_edit, 1)
        layout.addWidget(apply_btn)
        
        self.log_result("✅ Composite widget approach implemented")
    
    def test_overlay_approach(self):
        """Test overlay button approach."""
        self.overlay_widget = QWidget()
        self.overlay_widget.setFixedHeight(32)
        
        # Create line edit
        line_edit = QLineEdit(self.overlay_widget)
        line_edit.setPlaceholderText("Overlay approach...")
        line_edit.setGeometry(0, 0, 300, 32)
        
        # Create overlay button
        overlay_btn = QToolButton(self.overlay_widget)
        overlay_btn.setText("✓")
        overlay_btn.setFixedSize(24, 24)
        overlay_btn.clicked.connect(lambda: self.log_result("Overlay button clicked"))
        
        # Position button on the right side of line edit
        def position_button():
            btn_x = line_edit.width() - overlay_btn.width() - 4
            btn_y = (line_edit.height() - overlay_btn.height()) // 2
            overlay_btn.move(btn_x, btn_y)
        
        # Position initially and on resize
        position_button()
        line_edit.resizeEvent = lambda event: (
            QLineEdit.resizeEvent(line_edit, event),
            position_button()
        )
        
        self.log_result("✅ Overlay approach implemented")
    
    def log_result(self, message):
        """Log research results."""
        current_text = self.results_label.text()
        if "Click buttons" in current_text:
            self.results_label.setText(message)
        else:
            self.results_label.setText(current_text + "\n" + message)
        print(f"RESEARCH: {message}")


def main():
    """Run the research application."""
    app = QApplication(sys.argv)
    
    # Set dark theme for testing
    app.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: white;
        }
        QLineEdit {
            background-color: #1E1E1E;
            border: 1px solid #333333;
            border-radius: 4px;
            padding: 4px;
            color: white;
        }
        QLabel {
            color: white;
            font-weight: bold;
            margin: 10px 0 5px 0;
        }
    """)
    
    research_widget = IntegratedIconResearch()
    research_widget.show()
    
    print("=== PySide6 Integrated Icons Research ===")
    print("Testing various approaches for embedding buttons in QLineEdit...")
    print("Click the buttons in each test to verify functionality.")
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
