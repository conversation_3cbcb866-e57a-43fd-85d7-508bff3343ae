# Table Search & Filtering: Development Direction Summary

## Purpose
This document summarises the current and proposed development direction for TableView search and filtering, as discussed in recent technical and design threads.

---

## Background
- The user syntax discussion is now limited to the `search_syntax_discussion.md` document.
- Broader architectural and implementation direction is tracked here for clarity and future reference.

---

## Key Insights from Recent Discussions

- **Mission Creep Identified:**
  - Earlier docs and prototypes mixed user syntax, persistence, and backend search logic.
  - Clear separation is now enforced: user-facing syntax vs. implementation/architecture.

- **Recommended Approach:**
  - Use an existing, mature boolean search parser (e.g. `lucene-query-parser`) instead of maintaining a custom parser.
  - Preprocess user input to normalise all supported operator synonyms (`AND`, `OR`, `/`, `|`, `NOT`, `-`) to the package’s expected syntax.
  - All logical operators are always available and synonymous; use capitals for clarity.
  - Parentheses for grouping, quoted phrases for exact match, and case-insensitive matching are required.

- **Implementation Plan:**
  1. Preprocess input for operator synonyms.
  2. Parse using the chosen package.
  3. Evaluate and apply to data.

- **Extensibility:**
  - Optionally, allow users to select between different parser engines in the future (e.g. Google, Jira, GitHub styles).
  - Reference industry standards and popular apps for user familiarity.

- **Rationalisation:**
  - This approach balances UX, code simplicity, extensibility, and industry standards.
  - Reduces maintenance burden and risk of bugs compared to a custom parser.

---

## Next Steps
- Update docs to reflect this separation of concerns.
- Limit `search_syntax_discussion.md` to user syntax only.
- Use this document for ongoing design and implementation direction.
- Prototype integration with `lucene-query-parser` or similar.
- Review and refine based on team feedback and user testing.

---

## References
- See `search_syntax_discussion.md` for user-facing syntax details.
- See archived design docs for previous approaches and rationale.
