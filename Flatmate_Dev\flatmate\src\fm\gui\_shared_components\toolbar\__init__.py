"""
Standardized Toolbar Components

This package provides standardized toolbar components to ensure consistency
and maintainability across all toolbar implementations in the application.

Main Components:
- BaseToolbarButton: Standardized foundation for all toolbar buttons
- IntegratedTextButton: Specialized smaller buttons for text field integration
- IntegratedTextFieldAction: Helper for QLineEdit.addAction() integration
- Consistent styling, sizing, and behavior patterns
- Integrated icon loading and error handling

Usage:
    from fm.gui._shared_components.toolbar import BaseToolbarButton, IntegratedTextButton

    # Standard toolbar button
    button = BaseToolbarButton(
        icon_name="search",
        tooltip="Search data",
        style_variant="default"
    )

    # Smaller button for text field integration
    text_button = IntegratedTextButton(
        icon_name="check",
        tooltip="Apply",
        style_variant="apply"
    )
"""

from .base_toolbar_button import BaseToolbarButton
from .integrated_text_button import (
    IntegratedTextButton,
    IntegratedTextFieldAction,
    create_integrated_apply_button,
    create_integrated_clear_button,
    create_integrated_search_icon
)

__all__ = [
    'BaseToolbarButton',
    'IntegratedTextButton',
    'IntegratedTextFieldAction',
    'create_integrated_apply_button',
    'create_integrated_clear_button',
    'create_integrated_search_icon'
]
