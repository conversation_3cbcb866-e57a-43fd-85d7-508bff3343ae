"""
Standardized Toolbar Components

This package provides standardized toolbar components to ensure consistency
and maintainability across all toolbar implementations in the application.

Main Components:
- BaseToolbarButton: Standardized foundation for all toolbar buttons
- Consistent styling, sizing, and behavior patterns
- Integrated icon loading and error handling

Usage:
    from fm.gui._shared_components.toolbar import BaseToolbarButton
    
    button = BaseToolbarButton(
        icon_name="search",
        tooltip="Search data",
        style_variant="default"
    )
"""

from .base_toolbar_button import BaseToolbarButton

__all__ = ['BaseToolbarButton']
