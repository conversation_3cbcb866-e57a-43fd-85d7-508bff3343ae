"""
Base Toolbar Button Component

Standardized foundation for all toolbar buttons to ensure consistency
and maintainability across the application.

This component addresses the recurring implementation difficulties by providing:
- Consistent sizing and styling
- Standardized icon loading
- Proper error handling
- Multiple style variants for different use cases
"""

from PySide6.QtCore import QSize
from PySide6.QtWidgets import Q<PERSON>ushButton
from typing import Optional


class BaseToolbarButton(QPushButton):
    """Standardized base class for all toolbar buttons.
    
    Provides consistent sizing, styling, icon loading, and behavior
    for all toolbar buttons across the application.
    """
    
    # Style variants for different button types
    STYLE_VARIANTS = {
        "default": {
            "background": "transparent",
            "border": "1px solid #333333",
            "hover_bg": "rgba(255, 255, 255, 0.1)",
            "hover_border": "#3B8A45",
            "pressed_bg": "rgba(255, 255, 255, 0.2)"
        },
        "primary": {
            "background": "#3B8A45",
            "border": "none",
            "hover_bg": "#4BA357",
            "hover_border": "#4BA357",
            "pressed_bg": "#2E6E37"
        },
        "embedded": {
            "background": "transparent",
            "border": "none",
            "hover_bg": "rgba(255, 255, 255, 0.1)",
            "hover_border": "transparent",
            "pressed_bg": "rgba(255, 255, 255, 0.2)"
        }
    }
    
    def __init__(self, icon_name: Optional[str] = None, tooltip: Optional[str] = None, 
                 style_variant: str = "default", parent=None):
        """Initialize standardized toolbar button.
        
        Args:
            icon_name: Name of icon in toolbar category (e.g., "search", "clear")
            tooltip: Tooltip text for accessibility
            style_variant: Style variant ("default", "primary", "embedded")
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Standard sizing for all toolbar buttons
        self.setFixedSize(32, 32)
        
        # Set tooltip if provided
        if tooltip:
            self.setToolTip(tooltip)
        
        # Load icon if provided
        if icon_name:
            self._load_toolbar_icon(icon_name)
        
        # Apply styling
        self._apply_styling(style_variant)
    
    def _load_toolbar_icon(self, icon_name: str) -> None:
        """Load icon using standardized icon management system.
        
        Args:
            icon_name: Name of the icon to load from toolbar category
        """
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            
            icon_path = icon_manager.get_toolbar_icon(icon_name)
            icon = IconRenderer.load_icon(icon_path, QSize(16, 16))
            self.setIcon(icon)
            self.setIconSize(QSize(16, 16))
            
        except Exception as e:
            print(f"Warning: Could not load toolbar icon '{icon_name}': {e}")
            # Continue without icon - button will still function
    
    def _apply_styling(self, variant: str) -> None:
        """Apply consistent styling based on variant.
        
        Args:
            variant: Style variant name
        """
        style = self.STYLE_VARIANTS.get(variant, self.STYLE_VARIANTS["default"])
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {style["background"]};
                color: white;
                border: {style["border"]};
                border-radius: 4px;
                padding: 8px;
                min-width: 32px;
                max-width: 32px;
                min-height: 32px;
                max-height: 32px;
            }}
            QPushButton:hover {{
                background-color: {style["hover_bg"]};
                border-color: {style["hover_border"]};
            }}
            QPushButton:pressed {{
                background-color: {style["pressed_bg"]};
            }}
            QPushButton:disabled {{
                background-color: transparent;
                border-color: #555555;
                color: #888888;
            }}
        """)
    
    def update_icon(self, icon_name: str) -> None:
        """Update the button icon.
        
        Args:
            icon_name: New icon name to load
        """
        self._load_toolbar_icon(icon_name)
    
    def set_style_variant(self, variant: str) -> None:
        """Change the style variant of the button.
        
        Args:
            variant: New style variant to apply
        """
        self._apply_styling(variant)


# Convenience factory functions for common button types

def create_apply_button(parent=None) -> BaseToolbarButton:
    """Create a standardized apply button.
    
    Returns:
        BaseToolbarButton configured as apply button
    """
    return BaseToolbarButton(
        icon_name="check",
        tooltip="Apply filter",
        style_variant="primary",
        parent=parent
    )


def create_clear_button(parent=None) -> BaseToolbarButton:
    """Create a standardized clear button.
    
    Returns:
        BaseToolbarButton configured as clear button
    """
    return BaseToolbarButton(
        icon_name="clear",
        tooltip="Clear search",
        style_variant="default",
        parent=parent
    )


def create_export_button(parent=None) -> BaseToolbarButton:
    """Create a standardized export button.
    
    Returns:
        BaseToolbarButton configured as export button
    """
    return BaseToolbarButton(
        icon_name="export",
        tooltip="Export Data",
        style_variant="default",
        parent=parent
    )


def create_search_icon(parent=None) -> BaseToolbarButton:
    """Create a decorative search icon.
    
    Returns:
        BaseToolbarButton configured as decorative search icon
    """
    button = BaseToolbarButton(
        icon_name="search",
        tooltip="Search",
        style_variant="embedded",
        parent=parent
    )
    button.setEnabled(False)  # Decorative only
    return button
