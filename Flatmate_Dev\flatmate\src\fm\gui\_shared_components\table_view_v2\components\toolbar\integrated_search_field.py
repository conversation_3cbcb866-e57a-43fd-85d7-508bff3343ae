"""
Integrated Search Field Component

Advanced search field with embedded apply button inside the text field
and external clear button, designed for maximum text input space.

This component implements the refined search container layout:
- Apply button embedded inside text field (trailing position)
- Clear button positioned outside text field (right aligned)
- Search icon as decorative element (leading position)
- Maximum space prioritized for text input
"""

from PySide6.QtCore import Signal, QTimer, QSize
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLineEdit
from PySide6.QtGui import QAction
from fm.gui._shared_components.toolbar import IntegratedTextButton, create_integrated_clear_button


class IntegratedSearchField(QWidget):
    """Advanced search field with embedded apply button and external clear button.
    
    Layout Structure:
    [Search Icon] [Text Input with embedded Apply Button] [External Clear Button]
    
    Features:
    - Apply button embedded inside text field using QLineEdit.addAction()
    - Clear button positioned outside for easy access
    - Search icon as decorative leading element
    - Dynamic apply button (shows only when needed)
    - Maximum text input space utilization
    """
    
    # Signals for external communication
    filter_changed = Signal(str)  # Emits filter text as user types (live filtering)
    filter_requested = Signal(str)  # Emits filter text when apply button clicked
    advanced_operators_detected = Signal(bool)  # Emits True when advanced operators detected
    
    def __init__(self, parent=None):
        """Initialize the integrated search field."""
        super().__init__(parent)
        
        # Internal state
        self._apply_action = None  # Reference to embedded apply button
        self._has_advanced_operators = False
        
        # Debouncing timer for advanced operator detection
        self._operator_detection_timer = QTimer()
        self._operator_detection_timer.setSingleShot(True)
        self._operator_detection_timer.timeout.connect(self._check_operators_debounced)
        self._last_text = ""
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI with integrated search field layout."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)  # Rational spacing between components
        
        # Main text input field
        self._line_edit = QLineEdit()
        self._line_edit.setPlaceholderText("Search transactions... (e.g. coffee|tea, (coffee|tea) -decaf)")
        
        # Add decorative search icon (leading position)
        self._add_search_icon()
        
        # Style the line edit for integration
        self._line_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #333333;
                border-radius: 4px;
                background-color: #1E1E1E;
                color: white;
                padding: 6px 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3B8A45;
            }
        """)
        
        # Add line edit to layout (expandable)
        layout.addWidget(self._line_edit, 1)  # Stretch factor 1 for maximum expansion
        
        # External clear button (positioned outside text field)
        self._clear_button = create_integrated_clear_button()
        self._clear_button.setToolTip("Clear search")
        self._clear_button.hide()  # Hidden by default
        layout.addWidget(self._clear_button)
    
    def _add_search_icon(self):
        """Add decorative search icon to leading position."""
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            
            search_icon_path = icon_manager.get_toolbar_icon("search")
            search_icon = IconRenderer.load_icon(search_icon_path, QSize(14, 14))
            
            search_action = self._line_edit.addAction(search_icon, QLineEdit.LeadingPosition)
            search_action.setToolTip("Search")
            # No callback - decorative only
            
        except Exception as e:
            print(f"Warning: Could not load search icon: {e}")
    
    def _add_apply_button(self):
        """Add apply button to trailing position inside text field."""
        if self._apply_action is not None:
            return  # Already added
        
        try:
            from fm.gui.icons.icon_manager import icon_manager
            from fm.gui.icons.icon_renderer import IconRenderer
            
            check_icon_path = icon_manager.get_toolbar_icon("check")
            check_icon = IconRenderer.load_icon(check_icon_path, QSize(16, 16))
            
            self._apply_action = self._line_edit.addAction(check_icon, QLineEdit.TrailingPosition)
            self._apply_action.setToolTip("Apply filter")
            self._apply_action.triggered.connect(self._on_apply_clicked)
            
        except Exception as e:
            print(f"Warning: Could not load apply icon: {e}")
    
    def _remove_apply_button(self):
        """Remove apply button from text field."""
        if self._apply_action is not None:
            self._line_edit.removeAction(self._apply_action)
            self._apply_action = None
    
    def _connect_signals(self):
        """Connect internal signals."""
        self._line_edit.textChanged.connect(self._on_text_changed)
        self._line_edit.returnPressed.connect(self._on_return_pressed)
        self._clear_button.clicked.connect(self._on_clear_clicked)
    
    def _on_text_changed(self, text):
        """Handle text change for live filtering with dynamic apply button."""
        self._last_text = text
        
        # Show/hide external clear button based on text content
        if text.strip():
            self._clear_button.show()
        else:
            self._clear_button.hide()
        
        # Check for advanced operators with debouncing
        if self._is_likely_simple_query(text):
            # Fast path for obviously simple queries
            self._has_advanced_operators = False
            self.advanced_operators_detected.emit(False)
            self._remove_apply_button()  # Hide apply button for simple queries
            self.filter_changed.emit(text)  # Live filtering
        else:
            # Potentially complex query - use debounced detection
            self._operator_detection_timer.stop()
            self._operator_detection_timer.start(150)  # 150ms debounce
    
    def _on_return_pressed(self):
        """Handle Enter key press."""
        text = self._line_edit.text()
        self.filter_requested.emit(text)
    
    def _on_apply_clicked(self):
        """Handle apply button click."""
        text = self._line_edit.text()
        self.filter_requested.emit(text)
    
    def _on_clear_clicked(self):
        """Handle external clear button click."""
        self._line_edit.clear()
        # Text change will handle hiding clear button and apply button
    
    def _is_likely_simple_query(self, text):
        """Quick check for obviously simple queries to avoid unnecessary parsing."""
        if not text or len(text.strip()) < 2:
            return True
        
        # Check for obvious advanced operators
        advanced_chars = ['|', '(', ')', '-', '&', '"']
        return not any(char in text for char in advanced_chars)
    
    def _check_operators_debounced(self):
        """Debounced check for advanced operators."""
        text = self._last_text
        has_advanced = self._has_advanced_operators(text)
        
        if has_advanced != self._has_advanced_operators:
            self._has_advanced_operators = has_advanced
            self.advanced_operators_detected.emit(has_advanced)
            
            if has_advanced:
                self._add_apply_button()  # Show apply button for complex queries
            else:
                self._remove_apply_button()  # Hide for simple queries
                self.filter_changed.emit(text)  # Resume live filtering
    
    def _has_advanced_operators(self, text):
        """Check if text contains advanced operators."""
        if not text or not text.strip():
            return False
        
        # Simple heuristic for advanced operators
        # In a real implementation, this would use the actual query parser
        advanced_patterns = ['|', '(', ')', ' -', '&', '"']
        return any(pattern in text for pattern in advanced_patterns)
    
    # Public API methods for compatibility
    
    def text(self):
        """Get the current text."""
        return self._line_edit.text()
    
    def setText(self, text):
        """Set the text."""
        self._line_edit.setText(text)
    
    def clear(self):
        """Clear the text."""
        self._line_edit.clear()
    
    def setPlaceholderText(self, text):
        """Set placeholder text."""
        self._line_edit.setPlaceholderText(text)
    
    def setFocus(self):
        """Set focus to the text input."""
        self._line_edit.setFocus()
    
    def blockSignals(self, block):
        """Block signals (for compatibility)."""
        self._line_edit.blockSignals(block)
