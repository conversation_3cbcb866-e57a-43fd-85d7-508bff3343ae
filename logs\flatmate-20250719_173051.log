2025-07-19 17:30:51 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-19 17:30:53 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-19 17:30:53 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-19 17:30:53 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-19 17:30:53 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-19 17:30:54 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-19 17:30:54 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-19 17:30:54 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-19 17:30:54 - [main] [INFO] - Application starting...
2025-07-19 17:30:57 - [main] [INFO] - 
=== Initializing Database & Cache ===
2025-07-19 17:30:57 - [fm.core.data_services.db_io_service] [INFO] - Initializing DBIOService singleton...
2025-07-19 17:30:57 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-19 17:30:57 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - CachedSQLiteRepository initialized
2025-07-19 17:30:57 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Warming transaction cache...
2025-07-19 17:30:58 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Cache warmed successfully: 2099 transactions, 3 unique accounts in 1.02s
2025-07-19 17:30:58 - [fm.core.data_services.db_io_service] [INFO] - DBIOService initialized with cache: 2099 transactions
2025-07-19 17:30:58 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-19 17:30:58 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-19 17:30:58 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-19 17:30:58 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-19 17:30:58 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-19 17:30:58 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-19 17:30:58 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-19 17:30:58 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-19 17:30:58 - [fm.module_coordinator] [INFO] - Setting up home module
2025-07-19 17:30:58 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-19 17:30:58 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-19 17:30:58 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-07-19 17:30:58 - [fm.module_coordinator] [INFO] - Setting up update_data module
2025-07-19 17:30:58 - [fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-07-19 17:30:58 - [fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected
2025-07-19 17:30:58 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-07-19 17:30:58 - [fm.module_coordinator] [INFO] - Setting up categorize module
2025-07-19 17:30:58 - [fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-07-19 17:30:58 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - Cache hit: returning 3 unique accounts
2025-07-19 17:30:58 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-07-19 17:30:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-19 17:30:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-19 17:30:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.112s
2025-07-19 17:30:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-19 17:30:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - About to call _load_data_during_setup()
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Loading data during setup (eager loading)
2025-07-19 17:30:59 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:_load_data_during_setup)
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Filters: None
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Data Retrieval from Cache
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2099 transactions as DataFrame
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Data Retrieval from Cache took 0.213s
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame shape: (2099, 32), empty: False
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - First few rows:
              account  ...  is_deleted
0  38-9004-0646977-04  ...           0
1  38-9004-0646977-04  ...           0
2  38-9004-0646977-04  ...           0
3  38-9004-0646977-04  ...           0
4  38-9004-0646977-00  ...           0

[5 rows x 32 columns]
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Using DataFrame with shape: (2099, 32)
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Transaction Categorization
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Transaction Categorization took 0.121s
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._apply_default_sorting
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applied default sorting: date (descending)
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._apply_default_sorting took 0.008s
2025-07-19 17:30:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Table View Data Setting
2025-07-19 17:30:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2099 transactions to view
2025-07-19 17:30:59 - [fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2099 rows
2025-07-19 17:30:59 - [fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-19 17:30:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2099 rows
2025-07-19 17:31:00 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Using ordered display columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes']
2025-07-19 17:31:00 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Reordered DataFrame columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes', 'credit_amount', 'db_uid', 'debit_amount', 'empty', 'hash', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-19 17:31:06 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Date', 'Details', 'Amount', 'Account', 'Balance', 'Category', 'Tags', 'Notes']
2025-07-19 17:31:06 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Table View Data Setting took 6.486s
2025-07-19 17:31:06 - [fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2099 transactions in 7.1s (297.6 txns/s)
2025-07-19 17:31:06 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._handle_load_db took 7.071s
2025-07-19 17:31:06 - [fm.modules.categorize.cat_presenter] [DEBUG] - Data loading during setup complete
2025-07-19 17:31:06 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-07-19 17:31:06 - [fm.module_coordinator] [INFO] - All modules created and configured
2025-07-19 17:31:06 - [fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
2025-07-19 17:31:06 - [fm.module_coordinator] [INFO] - Starting Application
2025-07-19 17:31:06 - [fm.module_coordinator] [INFO] - Transitioning from None to home
2025-07-19 17:31:06 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-07-19 17:31:06 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-19 17:31:06 - [fm.modules.base.base_module_view] [INFO] - Setting up HomeView in Main Window
2025-07-19 17:31:06 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-19 17:31:06 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-19 17:31:06 - [fm.modules.base.base_module_view] [INFO] - HomeView setup complete
2025-07-19 17:31:06 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-19 17:31:06 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-19 17:31:06 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-19 17:31:06 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-19 17:31:06 - [main] [INFO] - 
=== Application Ready ===
2025-07-19 17:31:48 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to categorize
2025-07-19 17:31:48 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-19 17:31:48 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-07-19 17:31:48 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-07-19 17:31:48 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-19 17:31:48 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-19 17:31:48 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-07-19 17:31:48 - [fm.module_coordinator] [DEBUG] - Showing categorize module
2025-07-19 17:31:48 - [fm.modules.base.base_presenter] [INFO] - Showing CategorizePresenter
2025-07-19 17:31:48 - [fm.modules.base.base_module_view] [INFO] - Setting up CatView in Main Window
2025-07-19 17:31:48 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-19 17:31:48 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-19 17:31:48 - [fm.modules.base.base_module_view] [INFO] - CatView setup complete
2025-07-19 17:31:48 - [fm.modules.categorize.cat_presenter] [DEBUG] - Refreshing Categorize content (lightweight)
2025-07-19 17:31:48 - [fm.modules.categorize.cat_presenter] [DEBUG] - Categorize content refresh complete
2025-07-19 17:31:48 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter is now visible
2025-07-19 17:31:48 - [fm.module_coordinator] [INFO] - Successfully transitioned to categorize
