User Test Notes - 2025-07-19

We've been working on implementing the search tool and making sure that search and visible columns are integrated.

- when a row is selected (or a cell in that row) and you click out of the table view, that highlighting should not tuen the text dark grey 
the column can be highlighted as it is 
but the text shold remain white 
I need to be able to select text in the non eiditable columns (like details) And copy that text 

I also need to be able to select multiple cells in a column for applying a tag
and or apply to all matching - but this is getting into categorisation territory 
which involves some thought 
all matching ? by what citeria ? the current filtering and search terms ?

We still need to add the account naming 

Balance should be in default visible 
Balance is somewhat worthless in a unified data base of atomised transactions 

(not core - default visible )

Details needs to epxand to take up available space and ( may be being limited by max width  )

# Testing filtering logic 

this test, enter text`nbc -(` everythign disapears when I enter the`-`.
then `(` lags for quite some time before reappearing...
As soon as I try to enter anything into the brackets it completely chugs out 
nbc (x2 becomes imposisble 
it might be that live filtering is juts not compatible with more advanced filtering logic
 - we need to re think this ...

what does work very well and quickly ( apart from the `-` making everything disapear issue is nbc -xt -wk
And this is very intuitive 
imo 
no need for brackets

visible columns is a mess 

It has every possible colum including - `DB UID`
and `Source UID` AND `Unique ID` and `Is Deleted`

the only one of those it should have is Unique ID 
`Source Id` is some version of `source_uid`
which is A) a db name and b) not much use to users
`DB UID` is an internal ID and should not be visible 
`Is Deleted` is a system column and should not be a visible option

WhiloeI was testing as some point everything disapperared from table and would not come back 

I notice thigs are lagging out system wide so I suspect some recursive logi c valiuere that is not being caught ...

.flatmate\logs\flatmate-20250719_010725.log