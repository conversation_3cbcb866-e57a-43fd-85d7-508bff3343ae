"""
Test QLineEdit.addAction() Integration

Focused test to validate QLineEdit.addAction() approach for embedding
apply button inside text field, as required for the search container layout.
"""

import sys
from pathlib import Path

# Add the flatmate source to path for testing
sys.path.insert(0, str(Path(__file__).parent / "flatmate" / "src"))

from PySide6.QtWidgets import QApplication, QLineEdit, QVBoxLayout, QWidget, QLabel
from PySide6.QtGui import QAction
from PySide6.QtCore import QSize

try:
    from fm.gui.icons.icon_manager import icon_manager
    from fm.gui.icons.icon_renderer import IconRenderer
    ICONS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import icon system: {e}")
    ICONS_AVAILABLE = False


class QLineEditActionTest(QWidget):
    """Test QLineEdit.addAction() functionality for search container."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("QLineEdit.addAction() Test")
        self.setGeometry(100, 100, 500, 300)
        
        layout = QVBoxLayout(self)
        
        # Results display
        self.results_label = QLabel("Testing QLineEdit.addAction() integration...")
        layout.addWidget(self.results_label)
        
        # Test 1: Basic addAction functionality
        layout.addWidget(QLabel("Test 1: Basic addAction() - Apply button trailing"))
        self.test_basic_addaction()
        layout.addWidget(self.line_edit_1)
        
        # Test 2: Multiple actions (search + apply)
        layout.addWidget(QLabel("Test 2: Multiple actions - Search leading + Apply trailing"))
        self.test_multiple_actions()
        layout.addWidget(self.line_edit_2)
        
        # Test 3: Dynamic action behavior
        layout.addWidget(QLabel("Test 3: Dynamic behavior - Apply shows/hides based on content"))
        self.test_dynamic_actions()
        layout.addWidget(self.line_edit_3)
        
        layout.addWidget(self.results_label)
    
    def test_basic_addaction(self):
        """Test basic QLineEdit.addAction() functionality."""
        self.line_edit_1 = QLineEdit()
        self.line_edit_1.setPlaceholderText("Type here... apply button should appear on right")
        
        if ICONS_AVAILABLE:
            try:
                # Load apply icon
                check_icon_path = icon_manager.get_toolbar_icon("check")
                check_icon = IconRenderer.load_icon(check_icon_path, QSize(16, 16))
                
                # Add trailing action (right side)
                apply_action = self.line_edit_1.addAction(check_icon, QLineEdit.TrailingPosition)
                apply_action.setToolTip("Apply filter")
                apply_action.triggered.connect(lambda: self.log_result("✅ Basic apply action clicked"))
                
                self.log_result("✅ Basic addAction() successful - apply button added")
                
            except Exception as e:
                self.log_result(f"❌ Basic addAction() failed: {e}")
        else:
            # Fallback without icons
            apply_action = self.line_edit_1.addAction("✓", QLineEdit.TrailingPosition)
            apply_action.triggered.connect(lambda: self.log_result("✅ Basic apply action clicked (fallback)"))
            self.log_result("✅ Basic addAction() successful (fallback)")
    
    def test_multiple_actions(self):
        """Test multiple actions in one QLineEdit."""
        self.line_edit_2 = QLineEdit()
        self.line_edit_2.setPlaceholderText("Search icon left, apply button right")
        
        if ICONS_AVAILABLE:
            try:
                # Add search icon (leading/left)
                search_icon_path = icon_manager.get_toolbar_icon("search")
                search_icon = IconRenderer.load_icon(search_icon_path, QSize(14, 14))
                search_action = self.line_edit_2.addAction(search_icon, QLineEdit.LeadingPosition)
                search_action.setToolTip("Search")
                # No callback - decorative only
                
                # Add apply button (trailing/right)
                check_icon_path = icon_manager.get_toolbar_icon("check")
                check_icon = IconRenderer.load_icon(check_icon_path, QSize(16, 16))
                apply_action = self.line_edit_2.addAction(check_icon, QLineEdit.TrailingPosition)
                apply_action.setToolTip("Apply filter")
                apply_action.triggered.connect(lambda: self.log_result("✅ Multiple actions - apply clicked"))
                
                self.log_result("✅ Multiple actions successful - search + apply")
                
            except Exception as e:
                self.log_result(f"❌ Multiple actions failed: {e}")
        else:
            # Fallback
            self.line_edit_2.addAction("🔍", QLineEdit.LeadingPosition)
            apply_action = self.line_edit_2.addAction("✓", QLineEdit.TrailingPosition)
            apply_action.triggered.connect(lambda: self.log_result("✅ Multiple actions - apply clicked (fallback)"))
            self.log_result("✅ Multiple actions successful (fallback)")
    
    def test_dynamic_actions(self):
        """Test dynamic action behavior based on text content."""
        self.line_edit_3 = QLineEdit()
        self.line_edit_3.setPlaceholderText("Apply button appears when you type...")
        
        # Store action reference for dynamic behavior
        self.dynamic_apply_action = None
        
        # Connect text change to dynamic behavior
        self.line_edit_3.textChanged.connect(self._on_dynamic_text_changed)
        
        self.log_result("✅ Dynamic action test setup - type to see apply button")
    
    def _on_dynamic_text_changed(self, text):
        """Handle dynamic apply button show/hide."""
        has_text = bool(text.strip())
        
        if has_text and self.dynamic_apply_action is None:
            # Show apply button
            if ICONS_AVAILABLE:
                try:
                    check_icon_path = icon_manager.get_toolbar_icon("check")
                    check_icon = IconRenderer.load_icon(check_icon_path, QSize(16, 16))
                    self.dynamic_apply_action = self.line_edit_3.addAction(check_icon, QLineEdit.TrailingPosition)
                except:
                    self.dynamic_apply_action = self.line_edit_3.addAction("✓", QLineEdit.TrailingPosition)
            else:
                self.dynamic_apply_action = self.line_edit_3.addAction("✓", QLineEdit.TrailingPosition)
            
            self.dynamic_apply_action.setToolTip("Apply filter")
            self.dynamic_apply_action.triggered.connect(lambda: self.log_result("✅ Dynamic apply clicked"))
            self.log_result("✅ Apply button appeared dynamically")
            
        elif not has_text and self.dynamic_apply_action is not None:
            # Hide apply button
            self.line_edit_3.removeAction(self.dynamic_apply_action)
            self.dynamic_apply_action = None
            self.log_result("✅ Apply button hidden dynamically")
    
    def log_result(self, message):
        """Log test results."""
        current_text = self.results_label.text()
        if "Testing QLineEdit" in current_text:
            self.results_label.setText(message)
        else:
            self.results_label.setText(current_text + "\n" + message)
        print(f"ADDACTION_TEST: {message}")


def main():
    """Run the addAction test application."""
    app = QApplication(sys.argv)
    
    # Set dark theme for testing
    app.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: white;
        }
        QLineEdit {
            background-color: #1E1E1E;
            border: 1px solid #333333;
            border-radius: 4px;
            padding: 6px 8px;
            color: white;
            font-size: 14px;
        }
        QLineEdit:focus {
            border-color: #3B8A45;
        }
        QLabel {
            color: white;
            font-weight: bold;
            margin: 10px 0 5px 0;
        }
    """)
    
    test_widget = QLineEditActionTest()
    test_widget.show()
    
    print("=== QLineEdit.addAction() Test ===")
    print("Testing embedded button functionality for search container...")
    print("1. Check if apply button appears on the right")
    print("2. Test multiple actions (search + apply)")
    print("3. Test dynamic behavior (type in third field)")
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
