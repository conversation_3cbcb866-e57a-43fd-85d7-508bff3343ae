"""
Test Search Container Layout Implementation

This script tests the new search container layout with:
- Apply button embedded inside text field (trailing position)
- Clear button positioned outside text field (right aligned)
- Search icon as decorative element
- Maximum space for text input
"""

import sys
from pathlib import Path

# Add the flatmate source to path for testing
sys.path.insert(0, str(Path(__file__).parent / "flatmate" / "src"))

from PySide6.QtWidgets import QApplication, QVBoxLayout, QWidget, QLabel, QHBoxLayout
from PySide6.QtCore import QTimer

try:
    from fm.gui._shared_components.table_view_v2.components.toolbar.integrated_search_field import IntegratedSearchField
    from fm.gui._shared_components.table_view_v2.components.toolbar.groups.filter_group import FilterGroup
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import components: {e}")
    COMPONENTS_AVAILABLE = False


class SearchContainerLayoutTest(QWidget):
    """Test widget for the new search container layout."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Search Container Layout Test")
        self.setGeometry(100, 100, 800, 400)
        
        layout = QVBoxLayout(self)
        
        # Results display
        self.results_label = QLabel("Testing new search container layout...")
        layout.addWidget(self.results_label)
        
        if COMPONENTS_AVAILABLE:
            self.test_integrated_search_field()
            self.test_filter_group_integration()
        else:
            layout.addWidget(QLabel("❌ Components not available - check imports"))
        
        layout.addWidget(self.results_label)
    
    def test_integrated_search_field(self):
        """Test standalone IntegratedSearchField component."""
        layout = self.layout()
        
        layout.addWidget(QLabel("Test 1: Standalone IntegratedSearchField"))
        
        # Create integrated search field
        self.search_field = IntegratedSearchField()
        
        # Connect signals for testing
        self.search_field.filter_changed.connect(
            lambda text: self.log_result(f"✅ Live filter: '{text}'")
        )
        self.search_field.filter_requested.connect(
            lambda text: self.log_result(f"✅ Apply requested: '{text}'")
        )
        self.search_field.advanced_operators_detected.connect(
            lambda detected: self.log_result(f"✅ Advanced operators: {detected}")
        )
        
        layout.addWidget(self.search_field)
        
        self.log_result("✅ IntegratedSearchField created successfully")
        
        # Test instructions
        instructions = QLabel("""
        Test Instructions:
        1. Type simple text → should trigger live filtering
        2. Type 'coffee|tea' → should show apply button inside text field
        3. Clear button should appear outside text field when typing
        4. Search icon should be visible on the left (decorative)
        """)
        instructions.setStyleSheet("color: #888888; font-size: 12px; margin: 10px;")
        layout.addWidget(instructions)
    
    def test_filter_group_integration(self):
        """Test FilterGroup with integrated search field."""
        layout = self.layout()
        
        layout.addWidget(QLabel("Test 2: FilterGroup with IntegratedSearchField"))
        
        # Create filter group (should use IntegratedSearchField internally)
        self.filter_group = FilterGroup(live_filtering=True)
        
        # Connect signals for testing
        self.filter_group.filter_applied.connect(
            lambda text, column: self.log_result(f"✅ Filter applied: '{text}' on '{column}'")
        )
        
        layout.addWidget(self.filter_group)
        
        self.log_result("✅ FilterGroup with IntegratedSearchField created")
        
        # Test layout structure
        QTimer.singleShot(100, self.validate_layout_structure)
    
    def validate_layout_structure(self):
        """Validate the layout structure meets requirements."""
        try:
            # Check that FilterGroup contains IntegratedSearchField
            if hasattr(self.filter_group, 'filter_input'):
                if isinstance(self.filter_group.filter_input, IntegratedSearchField):
                    self.log_result("✅ FilterGroup uses IntegratedSearchField")
                else:
                    self.log_result("❌ FilterGroup not using IntegratedSearchField")
            
            # Check for search icon instead of text label
            if hasattr(self.filter_group, 'search_icon'):
                self.log_result("✅ Search icon present (replaces text label)")
            elif hasattr(self.filter_group, 'filter_label'):
                self.log_result("❌ Still using text label instead of icon")
            
            # Check column selector is present
            if hasattr(self.filter_group, 'column_selector'):
                self.log_result("✅ Column selector present")
            
            self.log_result("✅ Layout structure validation complete")
            
        except Exception as e:
            self.log_result(f"❌ Layout validation error: {e}")
    
    def log_result(self, message):
        """Log test results."""
        current_text = self.results_label.text()
        if "Testing new search" in current_text:
            self.results_label.setText(message)
        else:
            self.results_label.setText(current_text + "\n" + message)
        print(f"LAYOUT_TEST: {message}")


def main():
    """Run the search container layout test."""
    app = QApplication(sys.argv)
    
    # Set dark theme for testing
    app.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: white;
        }
        QLineEdit {
            background-color: #1E1E1E;
            border: 1px solid #333333;
            border-radius: 4px;
            padding: 6px 8px;
            color: white;
            font-size: 14px;
        }
        QLineEdit:focus {
            border-color: #3B8A45;
        }
        QLabel {
            color: white;
            font-weight: bold;
            margin: 10px 0 5px 0;
        }
        QComboBox {
            background-color: #1E1E1E;
            border: 1px solid #333333;
            border-radius: 4px;
            padding: 4px 8px;
            color: white;
        }
        QComboBox::drop-down {
            border: none;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid white;
        }
    """)
    
    test_widget = SearchContainerLayoutTest()
    test_widget.show()
    
    print("=== Search Container Layout Test ===")
    print("Testing the new integrated search container layout...")
    print("Expected behavior:")
    print("1. Apply button embedded inside text field (appears for complex queries)")
    print("2. Clear button outside text field (appears when typing)")
    print("3. Search icon replaces text label")
    print("4. Maximum space for text input")
    print("5. Proper signal handling for live filtering and apply actions")
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
